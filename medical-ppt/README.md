# 成都中医药大学附属医院智能门户型互联网医院项目PPT

## 项目简介

这是一个基于HTML5技术开发的医疗行业PPT展示系统，专为成都中医药大学附属医院（四川省中医医院）智能门户型互联网医院项目设计。该系统采用现代化的Web技术，提供流畅的演示体验和专业的医疗行业视觉设计。

## 功能特点

### 🎨 设计特色
- **医疗行业专业配色**：采用医疗蓝(#2c5aa0)、科技蓝(#4a90e2)、健康绿(#00a86b)的专业配色方案
- **现代化界面设计**：简洁大方的界面布局，符合现代审美标准
- **响应式设计**：支持不同屏幕尺寸，适配桌面端和移动端
- **中医元素融合**：融入太极、中药等中医文化元素

### 🚀 交互功能
- **多种导航方式**：支持键盘方向键、鼠标点击、触摸滑动
- **进度指示**：实时显示演示进度和页面导航
- **全屏模式**：支持全屏演示，提供沉浸式体验
- **侧边栏导航**：快速跳转到任意页面
- **平滑动画**：页面切换动画流畅自然

### 📱 技术特性
- **纯前端实现**：基于HTML5、CSS3、JavaScript，无需后端支持
- **现代浏览器兼容**：支持Chrome、Firefox、Safari、Edge等主流浏览器
- **轻量级部署**：可直接部署到Web服务器或CDN
- **容器化支持**：支持Docker容器化部署

## 项目结构

```
medical-ppt/
├── index.html          # 主页面文件
├── css/
│   ├── style.css       # 主样式文件
│   └── animations.css  # 动画样式文件
├── js/
│   └── main.js         # 主JavaScript文件
├── assets/             # 资源文件目录（图片、字体等）
└── README.md           # 项目说明文档
```

## 使用方法

### 本地运行
1. 下载项目文件到本地
2. 使用浏览器打开 `index.html` 文件
3. 开始演示

### 服务器部署
1. 将项目文件上传到Web服务器
2. 配置Web服务器指向项目根目录
3. 通过URL访问演示

### Docker部署
```bash
# 构建Docker镜像
docker build -t medical-ppt .

# 运行容器
docker run -p 80:80 medical-ppt
```

## 操作指南

### 导航控制
- **键盘导航**：
  - `←` / `→`：上一页/下一页
  - `Home`：跳转到首页
  - `End`：跳转到最后一页
  - `F11`：切换全屏模式
  - `Esc`：退出全屏模式

- **鼠标操作**：
  - 点击左右箭头按钮切换页面
  - 点击进度条快速跳转
  - 点击侧边栏菜单按钮打开导航

- **触摸操作**：
  - 左右滑动切换页面
  - 点击触摸按钮进行导航

### 功能按钮
- **全屏按钮**：进入/退出全屏演示模式
- **菜单按钮**：打开/关闭侧边栏导航
- **进度条**：显示当前进度，点击可快速跳转

## PPT内容概览

本PPT共包含16页内容，涵盖以下主要章节：

1. **封面页**：项目标题和基本信息
2. **目录页**：整体内容结构导览
3. **背景分析**：医疗数字化转型背景和挑战
4. **项目目标**：整体目标和核心指标
5. **系统架构**：技术架构和服务体系
6. **服务体验**：患者服务全景图
7. **医护赋能**：医护服务赋能体系
8. **AI应用**：AI大模型医疗应用概览
9. **预问诊系统**：AI预问诊价值分析
10. **中医特色**：中医数字化创新服务
11. **创新亮点**：项目核心创新点
12. **预期效益**：项目效益和成果预期
13. **实施路径**：项目实施计划和保障措施
14. **总结展望**：项目总结和未来规划
15. **致谢页**：感谢和联系信息

## 技术规格

- **开发语言**：HTML5, CSS3, JavaScript (ES6+)
- **CSS框架**：原生CSS，使用CSS Grid和Flexbox布局
- **JavaScript库**：原生JavaScript，无第三方依赖
- **字体图标**：Font Awesome 5.15.4
- **浏览器支持**：Chrome 60+, Firefox 55+, Safari 12+, Edge 79+
- **屏幕分辨率**：支持1280x720及以上分辨率

## 自定义配置

### 修改配色方案
在 `css/style.css` 文件中修改CSS变量：
```css
:root {
    --primary-color: #2c5aa0;    /* 主色调 */
    --secondary-color: #4a90e2;  /* 辅助色 */
    --accent-color: #00a86b;     /* 强调色 */
}
```

### 调整动画效果
在 `css/animations.css` 文件中修改动画参数：
```css
.slide-transition {
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}
```

### 修改页面内容
直接编辑 `index.html` 文件中对应的页面内容。

## 注意事项

1. **字体支持**：确保系统安装了所需字体，或使用Web字体
2. **图片资源**：如需添加图片，请放置在 `assets/` 目录下
3. **浏览器兼容**：建议使用最新版本的现代浏览器
4. **性能优化**：大型图片建议进行压缩优化
5. **移动端适配**：在移动设备上使用时，建议横屏观看

## 联系信息

- **项目名称**：成都中医药大学附属医院智能门户型互联网医院项目
- **开发时间**：2024年7月
- **技术支持**：如有技术问题，请联系项目开发团队

---

*本PPT系统专为医疗行业设计，体现了传统中医与现代科技的完美融合。*
