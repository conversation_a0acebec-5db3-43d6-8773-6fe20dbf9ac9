// 医疗PPT主要JavaScript功能
class MedicalPPT {
    constructor() {
        this.currentSlide = 1;
        this.totalSlides = 16;
        this.isAnimating = false;
        this.isFullscreen = false;
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.updateProgress();
        this.updateSlideCounter();
        this.initializeSlides();
    }
    
    bindEvents() {
        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (this.isAnimating) return;
            
            switch(e.key) {
                case 'ArrowLeft':
                case 'ArrowUp':
                    e.preventDefault();
                    this.prevSlide();
                    break;
                case 'ArrowRight':
                case 'ArrowDown':
                case ' ':
                    e.preventDefault();
                    this.nextSlide();
                    break;
                case 'Home':
                    e.preventDefault();
                    this.goToSlide(1);
                    break;
                case 'End':
                    e.preventDefault();
                    this.goToSlide(this.totalSlides);
                    break;
                case 'Escape':
                    e.preventDefault();
                    this.exitFullscreen();
                    break;
                case 'F11':
                    e.preventDefault();
                    this.toggleFullscreen();
                    break;
            }
        });
        
        // 导航按钮事件
        document.getElementById('prevBtn').addEventListener('click', () => {
            if (!this.isAnimating) this.prevSlide();
        });
        
        document.getElementById('nextBtn').addEventListener('click', () => {
            if (!this.isAnimating) this.nextSlide();
        });
        
        // 全屏按钮事件
        document.getElementById('fullscreenBtn').addEventListener('click', () => {
            this.toggleFullscreen();
        });
        
        // 菜单按钮事件
        document.getElementById('menuBtn').addEventListener('click', () => {
            this.toggleSidebar();
        });
        
        // 关闭侧边栏事件
        document.getElementById('closeSidebar').addEventListener('click', () => {
            this.closeSidebar();
        });
        
        // 目录链接事件
        document.querySelectorAll('.toc-list a').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const slideNum = parseInt(link.dataset.slide);
                this.goToSlide(slideNum);
                this.closeSidebar();
            });
        });
        
        // 触摸事件（移动端支持）
        let touchStartX = 0;
        let touchStartY = 0;
        
        document.addEventListener('touchstart', (e) => {
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
        });
        
        document.addEventListener('touchend', (e) => {
            if (this.isAnimating) return;
            
            const touchEndX = e.changedTouches[0].clientX;
            const touchEndY = e.changedTouches[0].clientY;
            const deltaX = touchEndX - touchStartX;
            const deltaY = touchEndY - touchStartY;
            
            // 判断是否为有效滑动
            if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
                if (deltaX > 0) {
                    this.prevSlide();
                } else {
                    this.nextSlide();
                }
            }
        });
        
        // 全屏状态变化事件
        document.addEventListener('fullscreenchange', () => {
            this.isFullscreen = !!document.fullscreenElement;
            this.updateFullscreenButton();
        });
        
        // 窗口大小变化事件
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }
    
    initializeSlides() {
        // 初始化所有幻灯片
        const slides = document.querySelectorAll('.slide');
        slides.forEach((slide, index) => {
            if (index === 0) {
                slide.classList.add('active');
            } else {
                slide.classList.remove('active');
            }
        });
        
        this.updateNavigationButtons();
    }
    
    nextSlide() {
        if (this.currentSlide < this.totalSlides) {
            this.goToSlide(this.currentSlide + 1);
        }
    }
    
    prevSlide() {
        if (this.currentSlide > 1) {
            this.goToSlide(this.currentSlide - 1);
        }
    }
    
    goToSlide(slideNumber) {
        if (slideNumber < 1 || slideNumber > this.totalSlides || slideNumber === this.currentSlide || this.isAnimating) {
            return;
        }
        
        this.isAnimating = true;
        
        const currentSlideElement = document.querySelector(`#slide-${this.currentSlide}`);
        const nextSlideElement = document.querySelector(`#slide-${slideNumber}`);
        
        if (!currentSlideElement || !nextSlideElement) {
            this.isAnimating = false;
            return;
        }
        
        // 确定动画方向
        const isNext = slideNumber > this.currentSlide;
        
        // 添加动画类
        if (isNext) {
            currentSlideElement.classList.add('leaving');
            nextSlideElement.classList.add('entering');
        } else {
            currentSlideElement.classList.add('leaving-next');
            nextSlideElement.classList.add('entering-prev');
        }
        
        // 显示下一张幻灯片
        nextSlideElement.classList.add('active');
        
        // 动画完成后清理
        setTimeout(() => {
            currentSlideElement.classList.remove('active', 'leaving', 'leaving-next');
            nextSlideElement.classList.remove('entering', 'entering-prev');
            
            this.currentSlide = slideNumber;
            this.updateProgress();
            this.updateSlideCounter();
            this.updateNavigationButtons();
            this.updateTOCActive();
            
            this.isAnimating = false;
        }, 500);
    }
    
    updateProgress() {
        const progressFill = document.querySelector('.progress-fill');
        const progress = (this.currentSlide / this.totalSlides) * 100;
        progressFill.style.width = `${progress}%`;
    }
    
    updateSlideCounter() {
        const counter = document.querySelector('.slide-counter');
        counter.textContent = `${this.currentSlide} / ${this.totalSlides}`;
    }
    
    updateNavigationButtons() {
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        
        prevBtn.disabled = this.currentSlide === 1;
        nextBtn.disabled = this.currentSlide === this.totalSlides;
    }
    
    updateTOCActive() {
        document.querySelectorAll('.toc-list a').forEach(link => {
            link.classList.remove('active');
            if (parseInt(link.dataset.slide) === this.currentSlide) {
                link.classList.add('active');
            }
        });
    }
    
    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('active');
    }
    
    closeSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.remove('active');
    }
    
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.log(`Error attempting to enable fullscreen: ${err.message}`);
            });
        } else {
            document.exitFullscreen();
        }
    }
    
    exitFullscreen() {
        if (document.fullscreenElement) {
            document.exitFullscreen();
        }
    }
    
    updateFullscreenButton() {
        const fullscreenBtn = document.getElementById('fullscreenBtn');
        const icon = fullscreenBtn.querySelector('i');
        
        if (this.isFullscreen) {
            icon.className = 'fas fa-compress';
            fullscreenBtn.title = '退出全屏';
        } else {
            icon.className = 'fas fa-expand';
            fullscreenBtn.title = '全屏';
        }
    }
    
    handleResize() {
        // 处理窗口大小变化
        // 可以在这里添加响应式逻辑
    }
    
    // 自动播放功能（可选）
    startAutoPlay(interval = 5000) {
        this.autoPlayInterval = setInterval(() => {
            if (this.currentSlide < this.totalSlides) {
                this.nextSlide();
            } else {
                this.stopAutoPlay();
            }
        }, interval);
    }
    
    stopAutoPlay() {
        if (this.autoPlayInterval) {
            clearInterval(this.autoPlayInterval);
            this.autoPlayInterval = null;
        }
    }
    
    // 添加幻灯片内容动画
    animateSlideContent(slideElement) {
        const elements = slideElement.querySelectorAll('[data-animate]');
        elements.forEach((element, index) => {
            setTimeout(() => {
                element.classList.add('animated');
            }, index * 200);
        });
    }
    
    // 数字计数动画
    animateCounter(element, target, duration = 2000) {
        let start = 0;
        const increment = target / (duration / 16);
        
        const timer = setInterval(() => {
            start += increment;
            element.textContent = Math.floor(start);
            
            if (start >= target) {
                element.textContent = target;
                clearInterval(timer);
            }
        }, 16);
    }
    
    // 图表动画
    animateChart(chartElement) {
        const bars = chartElement.querySelectorAll('.chart-bar');
        bars.forEach((bar, index) => {
            setTimeout(() => {
                bar.style.height = bar.dataset.height || '100%';
            }, index * 100);
        });
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.medicalPPT = new MedicalPPT();
    
    // 添加加载完成的视觉反馈
    document.body.classList.add('loaded');
    
    // 隐藏键盘提示（几秒后）
    setTimeout(() => {
        const hint = document.querySelector('.keyboard-hint');
        if (hint) {
            hint.style.opacity = '0.5';
        }
    }, 3000);
});

// 防止页面刷新时的意外行为
window.addEventListener('beforeunload', (e) => {
    // 可以在这里添加保存状态的逻辑
});

// 错误处理
window.addEventListener('error', (e) => {
    console.error('PPT Error:', e.error);
});

// 导出类以供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MedicalPPT;
}
