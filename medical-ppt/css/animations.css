/* 动画效果样式 */

/* 页面切换动画 */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOutLeft {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(-100px);
    }
}

@keyframes slideOutRight {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100px);
    }
}

/* 淡入动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 缩放动画 */
@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* 旋转动画 */
@keyframes rotateIn {
    from {
        opacity: 0;
        transform: rotate(-180deg) scale(0.5);
    }
    to {
        opacity: 1;
        transform: rotate(0deg) scale(1);
    }
}

/* 弹跳动画 */
@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.1);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* 打字机效果 */
@keyframes typewriter {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink {
    0%, 50% {
        border-color: transparent;
    }
    51%, 100% {
        border-color: currentColor;
    }
}

/* 进度条动画 */
@keyframes progressFill {
    from {
        width: 0%;
    }
    to {
        width: var(--progress-width, 0%);
    }
}

/* 封面页特殊动画 */
.cover-content .hospital-logo {
    animation: bounceIn 1s ease-out 0.5s both;
}

.cover-content .main-title {
    animation: fadeInUp 1s ease-out 1s both;
}

.cover-content .sub-title {
    animation: fadeInUp 1s ease-out 1.3s both;
}

.cover-content .cover-info {
    animation: fadeInUp 1s ease-out 1.6s both;
}

/* 目录页动画 */
.toc-section {
    animation: fadeInUp 0.6s ease-out both;
}

.toc-section:nth-child(1) { animation-delay: 0.1s; }
.toc-section:nth-child(2) { animation-delay: 0.2s; }
.toc-section:nth-child(3) { animation-delay: 0.3s; }
.toc-section:nth-child(4) { animation-delay: 0.4s; }
.toc-section:nth-child(5) { animation-delay: 0.5s; }
.toc-section:nth-child(6) { animation-delay: 0.6s; }

/* 图标动画 */
.toc-icon {
    animation: rotateIn 0.8s ease-out both;
}

.toc-section:nth-child(1) .toc-icon { animation-delay: 0.2s; }
.toc-section:nth-child(2) .toc-icon { animation-delay: 0.3s; }
.toc-section:nth-child(3) .toc-icon { animation-delay: 0.4s; }
.toc-section:nth-child(4) .toc-icon { animation-delay: 0.5s; }
.toc-section:nth-child(5) .toc-icon { animation-delay: 0.6s; }
.toc-section:nth-child(6) .toc-icon { animation-delay: 0.7s; }

/* 悬停动画 */
.toc-section:hover .toc-icon {
    animation: pulse 0.6s ease-in-out infinite;
}

/* 按钮动画 */
.nav-btn, .control-btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-btn:active, .control-btn:active {
    transform: scale(0.95);
}

/* 侧边栏动画 */
.sidebar {
    transition: right 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.toc-list a {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 进度条动画 */
.progress-fill {
    transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 页面加载动画 */
.slide.entering {
    animation: slideInRight 0.5s cubic-bezier(0.4, 0, 0.2, 1) both;
}

.slide.leaving {
    animation: slideOutLeft 0.5s cubic-bezier(0.4, 0, 0.2, 1) both;
}

.slide.entering-prev {
    animation: slideInLeft 0.5s cubic-bezier(0.4, 0, 0.2, 1) both;
}

.slide.leaving-next {
    animation: slideOutRight 0.5s cubic-bezier(0.4, 0, 0.2, 1) both;
}

/* 内容元素动画 */
.slide-title {
    animation: fadeInDown 0.6s ease-out 0.2s both;
}

.slide-body {
    animation: fadeInUp 0.6s ease-out 0.4s both;
}

/* 特殊效果 */
.medical-icon {
    transition: all 0.3s ease;
}

.medical-icon:hover {
    transform: scale(1.1) rotate(5deg);
    filter: drop-shadow(0 6px 12px rgba(0,0,0,0.4));
}

/* 响应式动画调整 */
@media (max-width: 768px) {
    .slide {
        transition: all 0.3s ease;
    }
    
    .cover-content .hospital-logo,
    .cover-content .main-title,
    .cover-content .sub-title,
    .cover-content .cover-info {
        animation-duration: 0.8s;
    }
    
    .toc-section {
        animation-duration: 0.4s;
    }
}

/* 减少动画效果（用户偏好） */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 加载状态动画 */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* 数据可视化动画 */
.chart-bar {
    animation: growUp 1s ease-out both;
}

.chart-bar:nth-child(1) { animation-delay: 0.1s; }
.chart-bar:nth-child(2) { animation-delay: 0.2s; }
.chart-bar:nth-child(3) { animation-delay: 0.3s; }
.chart-bar:nth-child(4) { animation-delay: 0.4s; }

@keyframes growUp {
    from {
        transform: scaleY(0);
        transform-origin: bottom;
    }
    to {
        transform: scaleY(1);
        transform-origin: bottom;
    }
}

/* 数字计数动画 */
.counter {
    animation: countUp 2s ease-out both;
}

@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
