/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 医疗主题色彩变量 */
:root {
    --primary-color: #2c5aa0;      /* 医疗蓝 */
    --secondary-color: #4a90e2;    /* 浅蓝 */
    --accent-color: #00a86b;       /* 医疗绿 */
    --text-primary: #333333;       /* 主文字 */
    --text-secondary: #666666;     /* 次要文字 */
    --text-light: #999999;         /* 浅色文字 */
    --background-light: #f8f9fa;   /* 浅色背景 */
    --background-white: #ffffff;   /* 白色背景 */
    --border-color: #e1e5e9;       /* 边框色 */
    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 20px rgba(0,0,0,0.15);
    --gradient-primary: linear-gradient(135deg, #2c5aa0 0%, #4a90e2 100%);
    --gradient-accent: linear-gradient(135deg, #00a86b 0%, #4ecdc4 100%);
}

/* 基础字体和布局 */
body {
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--background-light);
    overflow: hidden;
}

/* 导航栏样式 */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: var(--background-white);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-light);
    z-index: 1000;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-title {
    font-size: 18px;
    font-weight: 500;
    color: var(--primary-color);
}

.nav-center {
    display: flex;
    align-items: center;
    gap: 15px;
}

.progress-bar {
    width: 200px;
    height: 4px;
    background: var(--border-color);
    border-radius: 2px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    width: 4%;
    transition: width 0.3s ease;
}

.slide-counter {
    font-size: 14px;
    color: var(--text-secondary);
    min-width: 60px;
    text-align: center;
}

.nav-right {
    display: flex;
    gap: 10px;
}

.nav-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--background-light);
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    color: var(--text-secondary);
}

.nav-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    top: 60px;
    right: -350px;
    width: 350px;
    height: calc(100vh - 60px);
    background: var(--background-white);
    box-shadow: var(--shadow-medium);
    transition: right 0.3s ease;
    z-index: 999;
    overflow-y: auto;
}

.sidebar.active {
    right: 0;
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.sidebar-header h3 {
    color: var(--primary-color);
    font-size: 18px;
}

.close-btn {
    width: 30px;
    height: 30px;
    border: none;
    background: none;
    cursor: pointer;
    color: var(--text-secondary);
    font-size: 16px;
}

.close-btn:hover {
    color: var(--primary-color);
}

.toc-list {
    list-style: none;
    padding: 20px;
}

.toc-list li {
    margin-bottom: 8px;
}

.toc-list a {
    display: block;
    padding: 10px 15px;
    text-decoration: none;
    color: var(--text-primary);
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 14px;
}

.toc-list a:hover,
.toc-list a.active {
    background: var(--gradient-primary);
    color: white;
    transform: translateX(5px);
}

/* 主要内容区域 */
.presentation-container {
    margin-top: 60px;
    height: calc(100vh - 60px);
    position: relative;
    overflow: hidden;
}

.slides-wrapper {
    width: 100%;
    height: 100%;
    position: relative;
}

/* 幻灯片基础样式 */
.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transform: translateX(100px);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
}

.slide.active {
    opacity: 1;
    transform: translateX(0);
}

.slide.prev {
    transform: translateX(-100px);
}

.slide-content {
    width: 100%;
    max-width: 1000px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* 封面页样式 */
.cover-slide {
    padding: 0;
}

.cover-background {
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cover-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.cover-content {
    text-align: center;
    color: white;
    z-index: 1;
    position: relative;
}

.hospital-logo {
    margin-bottom: 30px;
}

.medical-icon {
    font-size: 80px;
    color: rgba(255, 255, 255, 0.9);
    filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));
}

.main-title {
    font-size: 42px;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.3;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.subtitle-line {
    font-size: 36px;
    font-weight: 600;
}

.project-title {
    font-size: 38px;
    font-weight: 700;
    color: #ffd700;
}

.sub-title {
    font-size: 24px;
    font-weight: 400;
    margin-bottom: 40px;
    opacity: 0.9;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.cover-info {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    opacity: 0.9;
}

.info-item i {
    font-size: 18px;
}

/* 普通页面样式 */
.slide-header {
    margin-bottom: 40px;
    text-align: center;
}

.slide-title {
    font-size: 36px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.slide-title i {
    font-size: 32px;
    color: var(--accent-color);
}

.slide-body {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 目录页网格布局 */
.toc-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    width: 100%;
}

.toc-section {
    background: var(--background-white);
    padding: 30px;
    border-radius: 16px;
    box-shadow: var(--shadow-light);
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.toc-section:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
    border-color: var(--primary-color);
}

.toc-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 20px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toc-icon i {
    font-size: 24px;
    color: white;
}

.toc-section h3 {
    font-size: 20px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.toc-section p {
    color: var(--text-secondary);
    font-size: 14px;
    line-height: 1.5;
}

/* 控制按钮 */
.controls {
    position: fixed;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 20px;
    z-index: 100;
}

.control-btn {
    width: 50px;
    height: 50px;
    border: none;
    background: var(--background-white);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-medium);
    transition: all 0.3s ease;
    color: var(--primary-color);
    font-size: 18px;
}

.control-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* 键盘提示 */
.keyboard-hint {
    position: fixed;
    bottom: 10px;
    right: 20px;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    z-index: 100;
    opacity: 0.8;
}

/* 背景页面样式 */
.background-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 40px;
    width: 100%;
}

.section-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    color: var(--primary-color);
}

.section-header i {
    font-size: 24px;
    color: var(--accent-color);
}

.section-header h3 {
    font-size: 24px;
    font-weight: 600;
}

/* 政策时间轴 */
.policy-items {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.policy-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 15px 20px;
    background: var(--background-white);
    border-radius: 12px;
    box-shadow: var(--shadow-light);
    border-left: 4px solid var(--primary-color);
}

.policy-year {
    font-size: 18px;
    font-weight: 700;
    color: var(--primary-color);
    min-width: 60px;
}

.policy-text {
    font-size: 16px;
    color: var(--text-primary);
}

/* 趋势统计 */
.trend-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
}

.stat-item {
    text-align: center;
    padding: 20px;
    background: var(--gradient-primary);
    border-radius: 12px;
    color: white;
}

.stat-number {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 14px;
    opacity: 0.9;
}

/* 需求列表 */
.demand-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.demand-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 20px;
    background: var(--background-white);
    border-radius: 12px;
    box-shadow: var(--shadow-light);
}

.demand-item i {
    font-size: 20px;
    color: var(--accent-color);
    width: 24px;
}

/* 挑战页面样式 */
.challenges-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    width: 100%;
}

.challenge-card {
    background: var(--background-white);
    padding: 30px;
    border-radius: 16px;
    box-shadow: var(--shadow-light);
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.challenge-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
    border-color: var(--primary-color);
}

.challenge-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.challenge-icon i {
    font-size: 32px;
    color: white;
}

.challenge-card h3 {
    font-size: 20px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.challenge-stats {
    display: flex;
    justify-content: space-around;
    margin: 20px 0;
    padding: 15px 0;
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
}

.challenge-stats .stat {
    text-align: center;
}

.challenge-stats .number {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
}

.challenge-stats .label {
    font-size: 12px;
    color: var(--text-secondary);
}

.challenge-card p {
    color: var(--text-secondary);
    font-size: 14px;
    line-height: 1.5;
}

/* 目标页面样式 */
.goals-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
    width: 100%;
    align-items: start;
}

.goal-pyramid {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.goal-level {
    padding: 20px;
    border-radius: 12px;
    text-align: center;
}

.goal-level.strategic {
    background: var(--gradient-primary);
    color: white;
}

.goal-level.business {
    background: var(--background-white);
    border: 2px solid var(--primary-color);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    text-align: left;
}

.goal-level.operational {
    background: var(--background-light);
    display: flex;
    flex-direction: column;
    gap: 15px;
    text-align: left;
}

.goal-content h3 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 10px;
}

.goal-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: var(--background-white);
    border-radius: 8px;
    box-shadow: var(--shadow-light);
}

.goal-item i {
    font-size: 18px;
    color: var(--accent-color);
}

.target-metrics {
    background: var(--background-white);
    padding: 30px;
    border-radius: 16px;
    box-shadow: var(--shadow-light);
}

.target-metrics h3 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 25px;
    font-size: 20px;
}

.metrics-grid {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.metric-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-radius: 12px;
    background: var(--background-light);
}

.metric-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.metric-icon i {
    font-size: 20px;
    color: white;
}

.metric-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
}

.metric-label {
    font-size: 14px;
    color: var(--text-secondary);
}

/* 架构图样式 */
.architecture-diagram {
    display: flex;
    flex-direction: column;
    gap: 30px;
    width: 100%;
}

.architecture-layer {
    background: var(--background-white);
    padding: 25px;
    border-radius: 16px;
    box-shadow: var(--shadow-light);
}

.architecture-layer h3 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: 600;
}

.layer-items {
    display: flex;
    justify-content: space-around;
    gap: 20px;
}

.arch-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 20px;
    background: var(--background-light);
    border-radius: 12px;
    flex: 1;
}

.arch-item i {
    font-size: 32px;
    color: var(--primary-color);
}

.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 20px;
}

.module-card {
    text-align: center;
    padding: 20px;
    background: var(--background-light);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.module-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.module-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 15px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.module-icon i {
    font-size: 24px;
    color: white;
}

.module-card h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 8px;
}

.module-card p {
    font-size: 12px;
    color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 15px;
    }

    .nav-title {
        font-size: 16px;
    }

    .progress-bar {
        width: 120px;
    }

    .slide {
        padding: 20px;
    }

    .main-title {
        font-size: 28px;
    }

    .sub-title {
        font-size: 18px;
    }

    .slide-title {
        font-size: 28px;
    }

    .toc-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .cover-info {
        flex-direction: column;
        gap: 15px;
    }

    .sidebar {
        width: 100%;
        right: -100%;
    }

    .challenges-grid {
        grid-template-columns: 1fr;
    }

    .goals-container {
        grid-template-columns: 1fr;
    }

    .goal-level.business {
        grid-template-columns: 1fr;
    }

    .layer-items {
        flex-direction: column;
    }

    .modules-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .background-content {
        gap: 30px;
    }

    .trend-stats {
        grid-template-columns: 1fr;
    }

    .patient-journey {
        flex-direction: column;
    }

    .journey-arrow {
        transform: rotate(90deg);
        margin: 10px 0;
    }

    .special-features {
        grid-template-columns: 1fr;
    }

    .empowerment-modules {
        grid-template-columns: 1fr;
    }

    .efficiency-metrics {
        grid-template-columns: 1fr;
    }
}

/* 患者服务体验页面样式 */
.patient-journey {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 40px;
    width: 100%;
}

.journey-stage {
    flex: 1;
    text-align: center;
    padding: 20px;
    background: var(--background-white);
    border-radius: 16px;
    box-shadow: var(--shadow-light);
    transition: all 0.3s ease;
}

.journey-stage:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.stage-header {
    margin-bottom: 20px;
}

.stage-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 15px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stage-icon i {
    font-size: 32px;
    color: white;
}

.stage-header h3 {
    font-size: 20px;
    font-weight: 600;
    color: var(--primary-color);
}

.stage-features {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: var(--background-light);
    border-radius: 8px;
    font-size: 14px;
}

.feature-item i {
    font-size: 16px;
    color: var(--accent-color);
    width: 20px;
}

.stage-benefit {
    padding: 12px;
    background: var(--gradient-accent);
    border-radius: 8px;
    color: white;
    font-weight: 600;
}

.journey-arrow {
    margin: 0 20px;
    color: var(--primary-color);
    font-size: 24px;
}

.special-features {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-top: 30px;
}

.feature-highlight {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 25px;
    background: var(--background-white);
    border-radius: 16px;
    box-shadow: var(--shadow-light);
    border-left: 4px solid var(--accent-color);
}

.highlight-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.highlight-icon i {
    font-size: 24px;
    color: white;
}

.highlight-content h4 {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 8px;
}

.highlight-content p {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 8px;
}

.highlight-stat {
    font-size: 12px;
    font-weight: 600;
    color: var(--accent-color);
    background: rgba(0, 168, 107, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
}

/* 医护赋能体系样式 */
.empowerment-system {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 40px;
}

.central-hub {
    text-align: center;
    margin-bottom: 40px;
    z-index: 2;
}

.hub-icon {
    width: 100px;
    height: 100px;
    margin: 0 auto 15px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-medium);
}

.hub-icon i {
    font-size: 40px;
    color: white;
}

.central-hub h3 {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-color);
}

.empowerment-modules {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    width: 100%;
}

.module-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 25px;
    background: var(--background-white);
    border-radius: 16px;
    box-shadow: var(--shadow-light);
    transition: all 0.3s ease;
    animation: fadeInUp 0.6s ease-out both;
    animation-delay: var(--delay, 0s);
}

.module-item:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.module-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-accent);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.module-icon i {
    font-size: 20px;
    color: white;
}

.module-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 8px;
}

.module-content p {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.5;
    margin-bottom: 12px;
}

.module-stats {
    display: flex;
    align-items: baseline;
    gap: 5px;
}

.stat-value {
    font-size: 20px;
    font-weight: 700;
    color: var(--accent-color);
}

.stat-label {
    font-size: 12px;
    color: var(--text-light);
}

.efficiency-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.metric-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 25px;
    background: var(--gradient-primary);
    border-radius: 16px;
    color: white;
    text-align: center;
}

.metric-card .metric-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
}

.metric-card .metric-icon i {
    font-size: 20px;
    color: white;
}

.metric-data {
    display: flex;
    align-items: baseline;
    gap: 5px;
    margin-bottom: 8px;
}

.metric-number {
    font-size: 28px;
    font-weight: 700;
}

.metric-unit {
    font-size: 14px;
    opacity: 0.8;
}

.metric-desc {
    font-size: 12px;
    opacity: 0.9;
}

/* AI概览页面样式 */
.ai-overview {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 40px;
    width: 100%;
}

.ai-architecture {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.arch-section h3,
.security-section h3 {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--primary-color);
    font-size: 18px;
    margin-bottom: 20px;
}

.arch-layers {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.arch-layer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: var(--gradient-primary);
    color: white;
    border-radius: 8px;
    font-size: 14px;
}

.layer-name {
    font-weight: 600;
}

.layer-desc {
    opacity: 0.9;
}

.security-features {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.security-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: var(--background-white);
    border-radius: 12px;
    box-shadow: var(--shadow-light);
}

.security-item i {
    font-size: 24px;
    color: var(--accent-color);
    width: 30px;
}

.security-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 4px;
}

.security-content p {
    font-size: 14px;
    color: var(--text-secondary);
}

.ai-applications h3 {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--primary-color);
    font-size: 20px;
    margin-bottom: 25px;
}

.applications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.app-card {
    text-align: center;
    padding: 20px;
    background: var(--background-white);
    border-radius: 12px;
    box-shadow: var(--shadow-light);
    transition: all 0.3s ease;
}

.app-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.app-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 15px;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.app-icon i {
    font-size: 24px;
    color: white;
}

.app-card h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.app-card p {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.4;
    margin-bottom: 15px;
}

.app-metrics {
    padding: 8px 12px;
    background: var(--background-light);
    border-radius: 6px;
}

.app-metrics .metric {
    font-size: 12px;
    font-weight: 600;
    color: var(--accent-color);
}

/* AI预问诊页面样式 */
.pre-diagnosis-system {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    width: 100%;
}

.system-workflow h3,
.system-benefits h3 {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--primary-color);
    font-size: 20px;
    margin-bottom: 25px;
}

.workflow-steps {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.workflow-step {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    background: var(--background-white);
    border-radius: 12px;
    box-shadow: var(--shadow-light);
}

.step-number {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 700;
    flex-shrink: 0;
}

.step-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.step-content p {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.workflow-arrow {
    text-align: center;
    color: var(--primary-color);
    font-size: 20px;
    margin: 10px 0;
}

.benefits-comparison {
    margin-bottom: 30px;
}

.comparison-item {
    background: var(--background-white);
    padding: 25px;
    border-radius: 16px;
    box-shadow: var(--shadow-light);
}

.before-after {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.before,
.after {
    text-align: center;
    flex: 1;
}

.before h4,
.after h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.time-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 15px;
    background: var(--background-light);
    border-radius: 12px;
    margin-bottom: 10px;
}

.time-indicator.improved {
    background: var(--gradient-accent);
    color: white;
}

.time-indicator i {
    font-size: 20px;
}

.time-indicator span {
    font-size: 24px;
    font-weight: 700;
}

.arrow {
    margin: 0 20px;
    color: var(--primary-color);
    font-size: 24px;
}

.improvement {
    text-align: center;
    padding: 12px;
    background: var(--gradient-primary);
    color: white;
    border-radius: 8px;
    font-weight: 600;
}

.impact-metrics {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
}

.impact-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    background: var(--background-white);
    border-radius: 12px;
    box-shadow: var(--shadow-light);
}

.impact-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.impact-icon i {
    font-size: 24px;
    color: white;
}

.impact-data {
    display: flex;
    flex-direction: column;
}

.impact-number {
    font-size: 28px;
    font-weight: 700;
    color: var(--primary-color);
}

.impact-label {
    font-size: 14px;
    color: var(--text-secondary);
}

/* 中医特色服务页面样式 */
.tcm-innovation {
    display: flex;
    flex-direction: column;
    gap: 30px;
    width: 100%;
}

.innovation-overview {
    margin-bottom: 20px;
}

.overview-card {
    background: var(--gradient-primary);
    color: white;
    padding: 30px;
    border-radius: 16px;
    text-align: center;
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 15px;
}

.card-header i {
    font-size: 32px;
}

.card-header h3 {
    font-size: 24px;
    font-weight: 600;
}

.overview-card p {
    font-size: 16px;
    line-height: 1.6;
    opacity: 0.95;
}

.tcm-services {
    display: grid;
    grid-template-columns: 1fr;
    gap: 30px;
}

.service-category {
    background: var(--background-white);
    padding: 25px;
    border-radius: 16px;
    box-shadow: var(--shadow-light);
}

.category-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--border-color);
}

.category-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.category-icon i {
    font-size: 20px;
    color: white;
}

.category-header h3 {
    font-size: 20px;
    font-weight: 600;
    color: var(--primary-color);
}

.service-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.service-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 20px;
    background: var(--background-light);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.service-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

.item-icon {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.item-icon i {
    font-size: 16px;
    color: white;
}

.item-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.item-content p {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.4;
    margin-bottom: 8px;
}

.accuracy {
    font-size: 12px;
    font-weight: 600;
    color: var(--accent-color);
    background: rgba(0, 168, 107, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
}

/* 创新亮点页面样式 */
.innovation-highlights {
    display: flex;
    flex-direction: column;
    gap: 30px;
    width: 100%;
}

.highlight-main {
    text-align: center;
    margin-bottom: 20px;
}

.main-innovation {
    background: var(--gradient-primary);
    color: white;
    padding: 40px;
    border-radius: 20px;
    position: relative;
    overflow: hidden;
}

.main-innovation::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

.innovation-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1;
}

.innovation-icon i {
    font-size: 32px;
    color: white;
}

.main-innovation h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 15px;
    position: relative;
    z-index: 1;
}

.main-innovation p {
    font-size: 16px;
    line-height: 1.6;
    opacity: 0.95;
    position: relative;
    z-index: 1;
}

.highlights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.highlight-card {
    background: var(--background-white);
    border-radius: 16px;
    box-shadow: var(--shadow-light);
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.highlight-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.highlight-number {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background: var(--gradient-accent);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 700;
}

.highlight-content {
    padding: 30px;
}

.highlight-content .highlight-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.highlight-content .highlight-icon i {
    font-size: 24px;
    color: white;
}

.highlight-content h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.highlight-features {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
}

.feature {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    color: var(--text-secondary);
}

.feature i {
    font-size: 12px;
    color: var(--accent-color);
    width: 16px;
}

.highlight-impact {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: var(--background-light);
    border-radius: 8px;
}

.impact-label {
    font-size: 14px;
    color: var(--text-secondary);
}

.impact-value {
    font-size: 20px;
    font-weight: 700;
    color: var(--accent-color);
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 预期效益页面样式 */
.benefits-overview {
    display: flex;
    flex-direction: column;
    gap: 40px;
    width: 100%;
}

.benefits-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
}

.benefit-category {
    background: var(--background-white);
    border-radius: 16px;
    box-shadow: var(--shadow-light);
    overflow: hidden;
    transition: all 0.3s ease;
}

.benefit-category:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.category-header {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 25px 25px 20px;
    background: var(--gradient-primary);
    color: white;
}

.category-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.category-icon i {
    font-size: 20px;
    color: white;
}

.category-header h3 {
    font-size: 18px;
    font-weight: 600;
}

.benefit-items {
    padding: 25px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.benefit-item {
    padding: 15px;
    background: var(--background-light);
    border-radius: 12px;
}

.benefit-metric {
    display: flex;
    align-items: baseline;
    gap: 10px;
    margin-bottom: 8px;
}

.metric-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--accent-color);
}

.metric-label {
    font-size: 14px;
    color: var(--text-secondary);
}

.benefit-item p {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.roi-summary {
    margin-top: 20px;
}

.roi-card {
    background: var(--gradient-accent);
    color: white;
    padding: 30px;
    border-radius: 16px;
    text-align: center;
}

.roi-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 25px;
}

.roi-header i {
    font-size: 28px;
}

.roi-header h3 {
    font-size: 22px;
    font-weight: 600;
}

.roi-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 30px;
}

.roi-metric {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.roi-value {
    font-size: 32px;
    font-weight: 700;
}

.roi-label {
    font-size: 14px;
    opacity: 0.9;
}

/* 实施路径页面样式 */
.implementation-plan {
    display: flex;
    flex-direction: column;
    gap: 40px;
    width: 100%;
}

.timeline-section h3,
.guarantee-section h3 {
    display: flex;
    align-items: center;
    gap: 15px;
    color: var(--primary-color);
    font-size: 22px;
    margin-bottom: 30px;
}

.timeline {
    position: relative;
    padding-left: 40px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 20px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--border-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 40px;
}

.timeline-marker {
    position: absolute;
    left: -28px;
    top: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 700;
    color: white;
    z-index: 2;
}

.timeline-marker.phase1 {
    background: var(--gradient-primary);
}

.timeline-marker.phase2 {
    background: var(--gradient-accent);
}

.timeline-marker.phase3 {
    background: var(--gradient-secondary);
}

.timeline-marker.phase4 {
    background: var(--gradient-warning);
}

.timeline-content {
    background: var(--background-white);
    padding: 25px;
    border-radius: 16px;
    box-shadow: var(--shadow-light);
    margin-left: 20px;
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.timeline-header h4 {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
}

.timeline-duration {
    font-size: 14px;
    color: var(--text-secondary);
    background: var(--background-light);
    padding: 6px 12px;
    border-radius: 6px;
}

.timeline-tasks {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.task-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: var(--background-light);
    border-radius: 8px;
    font-size: 14px;
}

.task-item i {
    font-size: 16px;
    color: var(--accent-color);
    width: 20px;
}

.guarantee-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
}

.guarantee-item {
    background: var(--background-white);
    padding: 25px;
    border-radius: 16px;
    box-shadow: var(--shadow-light);
    text-align: center;
    transition: all 0.3s ease;
}

.guarantee-item:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.guarantee-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 20px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.guarantee-icon i {
    font-size: 24px;
    color: white;
}

.guarantee-item h4 {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.guarantee-item ul {
    list-style: none;
    padding: 0;
    text-align: left;
}

.guarantee-item li {
    padding: 8px 0;
    font-size: 14px;
    color: var(--text-secondary);
    position: relative;
    padding-left: 20px;
}

.guarantee-item li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: var(--accent-color);
    font-weight: bold;
}

/* 总结与展望页面样式 */
.summary-content {
    display: flex;
    flex-direction: column;
    gap: 40px;
    width: 100%;
}

.project-summary {
    background: var(--background-white);
    padding: 40px;
    border-radius: 20px;
    box-shadow: var(--shadow-light);
    text-align: center;
}

.summary-header {
    margin-bottom: 40px;
}

.summary-icon {
    width: 100px;
    height: 100px;
    margin: 0 auto 20px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.summary-icon i {
    font-size: 40px;
    color: white;
}

.summary-header h2 {
    font-size: 28px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 10px;
    line-height: 1.3;
}

.summary-subtitle {
    font-size: 18px;
    color: var(--accent-color);
    font-weight: 600;
}

.key-achievements h3 {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    color: var(--primary-color);
    font-size: 22px;
    margin-bottom: 30px;
}

.achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 25px;
}

.achievement-item {
    text-align: center;
    padding: 25px;
    background: var(--background-light);
    border-radius: 16px;
    transition: all 0.3s ease;
}

.achievement-item:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-light);
}

.achievement-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 15px;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.achievement-icon i {
    font-size: 24px;
    color: white;
}

.achievement-item h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.achievement-item p {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.future-outlook {
    background: var(--background-white);
    padding: 30px;
    border-radius: 16px;
    box-shadow: var(--shadow-light);
}

.future-outlook h3 {
    display: flex;
    align-items: center;
    gap: 15px;
    color: var(--primary-color);
    font-size: 22px;
    margin-bottom: 30px;
}

.outlook-timeline {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
}

.outlook-phase {
    text-align: center;
    padding: 25px;
    background: var(--background-light);
    border-radius: 16px;
    position: relative;
}

.phase-marker {
    width: 60px;
    height: 60px;
    margin: 0 auto 20px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 700;
    color: white;
}

.phase-content h4 {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.phase-content ul {
    list-style: none;
    padding: 0;
    text-align: left;
}

.phase-content li {
    padding: 6px 0;
    font-size: 14px;
    color: var(--text-secondary);
    position: relative;
    padding-left: 20px;
}

.phase-content li::before {
    content: '▸';
    position: absolute;
    left: 0;
    color: var(--accent-color);
    font-weight: bold;
}

.call-to-action {
    background: var(--gradient-primary);
    color: white;
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.call-to-action::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: rotate 15s linear infinite;
}

.cta-content {
    position: relative;
    z-index: 1;
}

.cta-content h3 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 15px;
}

.cta-content p {
    font-size: 16px;
    opacity: 0.95;
    margin-bottom: 30px;
}

.cta-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 30px;
}

.cta-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.cta-stat .stat-number {
    font-size: 28px;
    font-weight: 700;
}

.cta-stat .stat-label {
    font-size: 14px;
    opacity: 0.9;
}

/* 谢谢页面样式 */
.thank-you-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.thank-you-main {
    margin-bottom: 40px;
    z-index: 2;
    position: relative;
}

.thank-you-icon {
    width: 120px;
    height: 120px;
    margin: 0 auto 30px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 2s ease-in-out infinite;
}

.thank-you-icon i {
    font-size: 50px;
    color: white;
}

.thank-you-title {
    font-size: 72px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.thank-you-subtitle {
    font-size: 36px;
    font-weight: 300;
    color: var(--secondary-color);
    margin-bottom: 20px;
    font-style: italic;
}

.thank-you-message {
    font-size: 20px;
    color: var(--text-secondary);
    font-weight: 500;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
    z-index: 2;
    position: relative;
}

.contact-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    font-size: 16px;
    color: var(--text-secondary);
}

.contact-item i {
    font-size: 18px;
    color: var(--accent-color);
}

.decorative-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
}

.floating-element {
    position: absolute;
    font-size: 24px;
    color: var(--accent-color);
    opacity: 0.3;
    animation: float var(--duration, 3s) ease-in-out infinite;
    animation-delay: var(--delay, 0s);
}

.floating-element:nth-child(1) {
    top: 20%;
    left: 10%;
}

.floating-element:nth-child(2) {
    top: 30%;
    right: 15%;
}

.floating-element:nth-child(3) {
    bottom: 25%;
    left: 15%;
}

.floating-element:nth-child(4) {
    bottom: 20%;
    right: 10%;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}
