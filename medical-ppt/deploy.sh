#!/bin/bash

# 成都中医药大学附属医院智能门户型互联网医院项目PPT部署脚本
# 作者：项目开发团队
# 日期：2024年7月

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目信息
PROJECT_NAME="medical-ppt"
CONTAINER_NAME="medical-ppt-presentation"
IMAGE_NAME="medical-ppt:latest"
PORT="8080"

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  医疗PPT项目部署脚本${NC}"
    echo -e "${BLUE}================================${NC}"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker服务未启动，请启动Docker服务"
        exit 1
    fi
    
    print_message "Docker环境检查通过"
}

# 检查docker-compose是否安装
check_docker_compose() {
    if ! command -v docker-compose &> /dev/null; then
        print_warning "docker-compose未安装，将使用docker命令部署"
        return 1
    fi
    return 0
}

# 停止并删除现有容器
cleanup_existing() {
    print_message "清理现有容器..."
    
    if docker ps -q -f name=$CONTAINER_NAME | grep -q .; then
        print_message "停止现有容器..."
        docker stop $CONTAINER_NAME
    fi
    
    if docker ps -aq -f name=$CONTAINER_NAME | grep -q .; then
        print_message "删除现有容器..."
        docker rm $CONTAINER_NAME
    fi
    
    if docker images -q $IMAGE_NAME | grep -q .; then
        print_message "删除现有镜像..."
        docker rmi $IMAGE_NAME
    fi
}

# 构建Docker镜像
build_image() {
    print_message "构建Docker镜像..."
    docker build -t $IMAGE_NAME .
    print_message "镜像构建完成"
}

# 使用docker-compose部署
deploy_with_compose() {
    print_message "使用docker-compose部署..."
    docker-compose up -d
    print_message "部署完成"
}

# 使用docker命令部署
deploy_with_docker() {
    print_message "使用docker命令部署..."
    docker run -d \
        --name $CONTAINER_NAME \
        -p $PORT:80 \
        --restart unless-stopped \
        $IMAGE_NAME
    print_message "部署完成"
}

# 检查部署状态
check_deployment() {
    print_message "检查部署状态..."
    
    if docker ps -f name=$CONTAINER_NAME | grep -q $CONTAINER_NAME; then
        print_message "容器运行正常"
        print_message "访问地址: http://localhost:$PORT"
        
        # 等待服务启动
        sleep 3
        
        # 检查服务是否可访问
        if curl -s -o /dev/null -w "%{http_code}" http://localhost:$PORT | grep -q "200"; then
            print_message "服务访问正常"
        else
            print_warning "服务可能还在启动中，请稍后访问"
        fi
    else
        print_error "容器启动失败"
        docker logs $CONTAINER_NAME
        exit 1
    fi
}

# 显示使用说明
show_usage() {
    echo "使用方法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -c, --clean    清理现有部署"
    echo "  -b, --build    仅构建镜像"
    echo "  -d, --deploy   仅部署服务"
    echo "  -s, --status   检查部署状态"
    echo ""
    echo "示例:"
    echo "  $0              # 完整部署流程"
    echo "  $0 --clean      # 清理现有部署"
    echo "  $0 --build      # 仅构建镜像"
    echo "  $0 --status     # 检查状态"
}

# 主函数
main() {
    print_header
    
    case "${1:-}" in
        -h|--help)
            show_usage
            exit 0
            ;;
        -c|--clean)
            check_docker
            cleanup_existing
            print_message "清理完成"
            exit 0
            ;;
        -b|--build)
            check_docker
            build_image
            exit 0
            ;;
        -d|--deploy)
            check_docker
            if check_docker_compose; then
                deploy_with_compose
            else
                deploy_with_docker
            fi
            check_deployment
            exit 0
            ;;
        -s|--status)
            check_deployment
            exit 0
            ;;
        "")
            # 完整部署流程
            check_docker
            cleanup_existing
            build_image
            
            if check_docker_compose; then
                deploy_with_compose
            else
                deploy_with_docker
            fi
            
            check_deployment
            
            print_message "部署完成！"
            print_message "请在浏览器中访问: http://localhost:$PORT"
            ;;
        *)
            print_error "未知选项: $1"
            show_usage
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
