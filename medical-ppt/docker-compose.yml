version: '3.8'

services:
  medical-ppt:
    build: .
    container_name: medical-ppt-presentation
    ports:
      - "8080:80"
    restart: unless-stopped
    environment:
      - NGINX_HOST=localhost
      - NGINX_PORT=80
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.medical-ppt.rule=Host(`medical-ppt.local`)"
      - "traefik.http.services.medical-ppt.loadbalancer.server.port=80"
    networks:
      - medical-network

networks:
  medical-network:
    driver: bridge
    name: medical-ppt-network
