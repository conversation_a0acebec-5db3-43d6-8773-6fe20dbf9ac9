


成都中医药大学附属医院（四川省中医医院）
智能门户型互联网医院建设方案














人工智能与信息化部
2025年07月
目录
 TOC \O "1-3" \H \Z \U  HYPERLINK \L _TOC15807 第1章 项目概述	 PAGEREF _TOC15807 \H 1
 HYPERLINK \L _TOC12458 1.1 项目名称	 PAGEREF _TOC12458 \H 1
 HYPERLINK \L _TOC21673 1.2 项目单位及负责人	 PAGEREF _TOC21673 \H 1
 HYPERLINK \L _TOC1880 1.3 建设方案编制单位	 PAGEREF _TOC1880 \H 1
 HYPERLINK \L _TOC5232 1.4 建设方案编制依据	 PAGEREF _TOC5232 \H 1
 HYPERLINK \l _Toc14892 1.4.1 政策要求：响应国家与地方战略部署，推动医疗服务数字化转型	 PAGEREF _Toc14892 \h 1
 HYPERLINK \l _Toc27807 1.4.2 医院发展：破解现有痛点，推动高质量发展与中医特色化转型	 PAGEREF _Toc27807 \h 2
 HYPERLINK \l _Toc2623 1.4.3 患者服务：满足多元化需求，构建以患者为中心的服务生态	 PAGEREF _Toc2623 \h 3
 HYPERLINK \L _TOC29345 1.5 项目建设目标、内容、建设周期	 PAGEREF _TOC29345 \H 3
 HYPERLINK \l _Toc5416 1.5.1 项目建设目标	 PAGEREF _Toc5416 \h 3
 HYPERLINK \l _Toc31804 1.5.2 项目建设内容	 PAGEREF _Toc31804 \h 6
 HYPERLINK \l _Toc30194 1.5.3 项目建设周期与地点	 PAGEREF _Toc30194 \h 7
 HYPERLINK \L _TOC27435 1.6 项目总投资及资金来源	 PAGEREF _TOC27435 \H 7
 HYPERLINK \L _TOC22243 1.7 经济与社会效益	 PAGEREF _TOC22243 \H 7
 HYPERLINK \l _Toc6723 1.7.1 经济效益	 PAGEREF _Toc6723 \h 7
 HYPERLINK \l _Toc17017 1.7.2 社会效益	 PAGEREF _Toc17017 \h 7
 HYPERLINK \L _TOC8455 1.8 主要结论与建议	 PAGEREF _TOC8455 \H 8
 HYPERLINK \L _TOC13632 第2章 项目单位概况	 PAGEREF _TOC13632 \H 10
 HYPERLINK \L _TOC2201 2.1 项目建设单位与职能	 PAGEREF _TOC2201 \H 10
 HYPERLINK \L _TOC16243 2.2 项目实施单位与职能	 PAGEREF _TOC16243 \H 11
 HYPERLINK \L _TOC9571 第3章 建设必要性	 PAGEREF _TOC9571 \H 12
 HYPERLINK \L _TOC15494 3.1 项目建设背景	 PAGEREF _TOC15494 \H 12
 HYPERLINK \l _Toc8953 3.1.1 政策背景	 PAGEREF _Toc8953 \h 12
 HYPERLINK \l _Toc6144 3.1.2 规范和指引	 PAGEREF _Toc6144 \h 15
 HYPERLINK \l _Toc31733 3.1.3 新技术支撑	 PAGEREF _Toc31733 \h 17
 HYPERLINK \L _TOC4706 3.2 现状分析	 PAGEREF _TOC4706 \H 18
 HYPERLINK \l _Toc9704 3.2.1 现状及痛点	 PAGEREF _Toc9704 \h 18
 HYPERLINK \l _Toc19935 3.2.2 发展要求和需求	 PAGEREF _Toc19935 \h 19
 HYPERLINK \L _TOC14736 第4章 需求分析	 PAGEREF _TOC14736 \H 22
 HYPERLINK \L _TOC27184 4.1 互联网医院用户需求分析	 PAGEREF _TOC27184 \H 22
 HYPERLINK \l _Toc25068 4.1.1 患者端应用	 PAGEREF _Toc25068 \h 22
 HYPERLINK \l _Toc28628 4.1.2 医务端应用	 PAGEREF _Toc28628 \h 25
 HYPERLINK \l _Toc28917 4.1.3 互联网医院运营平台（Web）	 PAGEREF _Toc28917 \h 28
 HYPERLINK \L _TOC3702 4.2 运营需求分析	 PAGEREF _TOC3702 \H 29
 HYPERLINK \l _Toc12025 4.2.1 短期运营需求分析	 PAGEREF _Toc12025 \h 29
 HYPERLINK \l _Toc3917 4.2.2 中期运营需求分析	 PAGEREF _Toc3917 \h 29
 HYPERLINK \l _Toc25067 4.2.3 长期运营需求分析	 PAGEREF _Toc25067 \h 29
 HYPERLINK \L _TOC9137 4.3 用户界面（UI）与使用习惯分析	 PAGEREF _TOC9137 \H 30
 HYPERLINK \l _Toc13356 4.3.1 用户群体特性分析	 PAGEREF _Toc13356 \h 30
 HYPERLINK \l _Toc14660 4.3.2 用户界面（UI）设计需求分析	 PAGEREF _Toc14660 \h 30
 HYPERLINK \l _Toc7826 4.3.3 使用习惯需求分析	 PAGEREF _Toc7826 \h 32
 HYPERLINK \L _TOC2133 第5章 建设方案	 PAGEREF _TOC2133 \H 34
 HYPERLINK \L _TOC22460 5.1 建设原则	 PAGEREF _TOC22460 \H 34
 HYPERLINK \l _Toc30311 5.1.1 整体规划，分步实施	 PAGEREF _Toc30311 \h 34
 HYPERLINK \l _Toc6900 5.1.2 前瞻性和先进性原则	 PAGEREF _Toc6900 \h 34
 HYPERLINK \l _Toc30223 5.1.3 实用性和可行性原则	 PAGEREF _Toc30223 \h 35
 HYPERLINK \l _Toc27437 5.1.4 安全性和可靠性原则	 PAGEREF _Toc27437 \h 35
 HYPERLINK \l _Toc5751 5.1.5 易用性和扩展性原则	 PAGEREF _Toc5751 \h 35
 HYPERLINK \L _TOC23261 5.2 建设思路	 PAGEREF _TOC23261 \H 35
 HYPERLINK \l _Toc18509 5.2.1 以患者为中心，优化线上服务	 PAGEREF _Toc18509 \h 36
 HYPERLINK \l _Toc26566 5.2.2 构建智能化医疗协同平台	 PAGEREF _Toc26566 \h 36
 HYPERLINK \l _Toc19182 5.2.3 强化数据互通与安全共享	 PAGEREF _Toc19182 \h 36
 HYPERLINK \l _Toc12781 5.2.4 支持分级治疗与区域医疗协同	 PAGEREF _Toc12781 \h 36
 HYPERLINK \L _TOC3072 5.3 总体架构	 PAGEREF _TOC3072 \H 37
 HYPERLINK \l _Toc20294 5.3.1 “1个互联网医院门户”：中医特色互联网医院门户	 PAGEREF _Toc20294 \h 37
 HYPERLINK \l _Toc21732 5.3.2 “运营服务”：运营服务体系	 PAGEREF _Toc21732 \h 40
 HYPERLINK \L _TOC18923 5.4 业务架构	 PAGEREF _TOC18923 \H 41
 HYPERLINK \l _Toc15520 5.4.1 患者就医业务流程（线上线下一体化）	 PAGEREF _Toc15520 \h 42
 HYPERLINK \l _Toc1235 5.4.2 亚健康人群治未病业务流程	 PAGEREF _Toc1235 \h 43
 HYPERLINK \l _Toc18995 5.4.3 门诊患者转治未病	 PAGEREF _Toc18995 \h 45
 HYPERLINK \l _Toc25436 5.4.4 治未病转门诊就医	 PAGEREF _Toc25436 \h 46
 HYPERLINK \L _TOC21105 5.5 安全架构	 PAGEREF _TOC21105 \H 46
 HYPERLINK \l _Toc12851 5.5.1 安全建设目标	 PAGEREF _Toc12851 \h 46
 HYPERLINK \l _Toc13786 5.5.2 安全建设原则	 PAGEREF _Toc13786 \h 46
 HYPERLINK \l _Toc1108 5.5.3 数据存储安全	 PAGEREF _Toc1108 \h 47
 HYPERLINK \l _Toc1542 5.5.4 数据备份与恢复策略	 PAGEREF _Toc1542 \h 47
 HYPERLINK \l _Toc3390 5.5.5 数据传输安全	 PAGEREF _Toc3390 \h 48
 HYPERLINK \l _Toc6047 5.5.6 数据访问安全	 PAGEREF _Toc6047 \h 48
 HYPERLINK \l _Toc2445 5.5.7 网络访问安全	 PAGEREF _Toc2445 \h 49
 HYPERLINK \l _Toc27907 5.5.8 应用访问安全	 PAGEREF _Toc27907 \h 50
 HYPERLINK \L _TOC4499 5.6 网络架构	 PAGEREF _TOC4499 \H 51
 HYPERLINK \l _Toc1785 5.6.1 本地中心与云中心之间网络架构	 PAGEREF _Toc1785 \h 51
 HYPERLINK \l _Toc19894 5.6.2 本地中心本地化网络架构	 PAGEREF _Toc19894 \h 52
 HYPERLINK \L _TOC21543 5.7 技术架构	 PAGEREF _TOC21543 \H 53
 HYPERLINK \l _Toc651 5.7.1 标准化质控体系	 PAGEREF _Toc651 \h 53
 HYPERLINK \l _Toc32044 5.7.2 标准化实施交付	 PAGEREF _Toc32044 \h 54
 HYPERLINK \l _Toc23309 5.7.3 标准化安全运维	 PAGEREF _Toc23309 \h 54
 HYPERLINK \l _Toc31369 5.7.4 数据安全技术体系	 PAGEREF _Toc31369 \h 54
 HYPERLINK \l _Toc18795 5.7.5 数据+业务双中台架构	 PAGEREF _Toc18795 \h 54
 HYPERLINK \l _Toc2742 5.7.6 人工智能技术	 PAGEREF _Toc2742 \h 55
 HYPERLINK \L _TOC23344 5.8 部署架构	 PAGEREF _TOC23344 \H 55
 HYPERLINK \l _Toc4641 5.8.1 整体部署架构图	 PAGEREF _Toc4641 \h 56
 HYPERLINK \l _Toc990 5.8.2 部署环境	 PAGEREF _Toc990 \h 60
 HYPERLINK \l _Toc31722 5.8.3 系统接口设计	 PAGEREF _Toc31722 \h 63
 HYPERLINK \L _TOC14640 5.9 产品性能设计	 PAGEREF _TOC14640 \H 65
 HYPERLINK \l _Toc10635 5.9.1 页面性能优化	 PAGEREF _Toc10635 \h 65
 HYPERLINK \l _Toc22178 5.9.2 数据库优化	 PAGEREF _Toc22178 \h 67
 HYPERLINK \l _Toc12507 5.9.3 监控关键运行指标	 PAGEREF _Toc12507 \h 67
 HYPERLINK \l _Toc10728 5.9.4 可维护性设计	 PAGEREF _Toc10728 \h 68
 HYPERLINK \l _Toc25352 5.9.5 便捷性设计	 PAGEREF _Toc25352 \h 68
 HYPERLINK \L _TOC22657 5.10 视觉设计	 PAGEREF _TOC22657 \H 69
 HYPERLINK \l _Toc11387 5.10.1 色彩运用	 PAGEREF _Toc11387 \h 69
 HYPERLINK \l _Toc3725 5.10.2 图标与图形设计	 PAGEREF _Toc3725 \h 69
 HYPERLINK \l _Toc7366 5.10.3 界面布局	 PAGEREF _Toc7366 \h 69
 HYPERLINK \l _Toc10627 5.10.4 交互设计	 PAGEREF _Toc10627 \h 70
 HYPERLINK \l _Toc7940 5.10.5 响应式设计	 PAGEREF _Toc7940 \h 70
 HYPERLINK \l _Toc25990 5.10.6 情感化设计	 PAGEREF _Toc25990 \h 70
 HYPERLINK \L _TOC28257 第6章 系统详细设计	 PAGEREF _TOC28257 \H 71
 HYPERLINK \L _TOC2658 6.1 数据洞察分析	 PAGEREF _TOC2658 \H 71
 HYPERLINK \l _Toc28567 6.1.1 患者画像分析	 PAGEREF _Toc28567 \h 71
 HYPERLINK \l _Toc15741 6.1.2 中医健康食品洞察分析	 PAGEREF _Toc15741 \h 71
 HYPERLINK \L _TOC8105 6.2 互联网门户功能设计	 PAGEREF _TOC8105 \H 72
 HYPERLINK \l _Toc2110 6.2.1 患者端功能设计	 PAGEREF _Toc2110 \h 72
 HYPERLINK \l _Toc2339 6.2.2 医护端功能设计	 PAGEREF _Toc2339 \h 87
 HYPERLINK \l _Toc24246 6.2.3 综合管理后台功能设计	 PAGEREF _Toc24246 \h 95
 HYPERLINK \L _TOC31910 6.3 互联网医院运营服务	 PAGEREF _TOC31910 \H 107
 HYPERLINK \l _Toc4242 6.3.1 运营策略	 PAGEREF _Toc4242 \h 107
 HYPERLINK \l _Toc28297 6.3.2 IP打造运营	 PAGEREF _Toc28297 \h 108
 HYPERLINK \l _Toc7839 6.3.3 线上流量运营	 PAGEREF _Toc7839 \h 112
 HYPERLINK \l _Toc10969 6.3.4 线下活动与场景融合	 PAGEREF _Toc10969 \h 115
 HYPERLINK \L _TOC359 第7章 安全体系设计方案	 PAGEREF _TOC359 \H 118
 HYPERLINK \L _TOC23038 7.1 建设目标	 PAGEREF _TOC23038 \H 118
 HYPERLINK \L _TOC4521 7.2 设计依据	 PAGEREF _TOC4521 \H 118
 HYPERLINK \L _TOC25541 7.3 现状及风险需求分析	 PAGEREF _TOC25541 \H 119
 HYPERLINK \l _Toc2503 7.3.1 安全技术体系需求分析	 PAGEREF _Toc2503 \h 119
 HYPERLINK \l _Toc27568 7.3.2 安全管理体系需求分析	 PAGEREF _Toc27568 \h 122
 HYPERLINK \l _Toc3927 7.3.3 大模型安全需求分析	 PAGEREF _Toc3927 \h 123
 HYPERLINK \L _TOC14510 7.4 总体设计	 PAGEREF _TOC14510 \H 124
 HYPERLINK \l _Toc28277 7.4.1 区域划分	 PAGEREF _Toc28277 \h 124
 HYPERLINK \l _Toc11180 7.4.2 总体安全部署	 PAGEREF _Toc11180 \h 124
 HYPERLINK \l _Toc15508 7.4.3 安全技术体系设计	 PAGEREF _Toc15508 \h 126
 HYPERLINK \l _Toc17313 7.4.4 安全管理体系设计	 PAGEREF _Toc17313 \h 132
 HYPERLINK \l _Toc1755 7.4.5 大模型安全设计（大模型脱敏罩）	 PAGEREF _Toc1755 \h 155
 HYPERLINK \L _TOC25343 第8章 售后服务方案	 PAGEREF _TOC25343 \H 161
 HYPERLINK \L _TOC4170 8.1 售后服务期限	 PAGEREF _TOC4170 \H 161
 HYPERLINK \L _TOC27060 8.2 售后服务内容	 PAGEREF _TOC27060 \H 161
 HYPERLINK \l _Toc1604 8.2.1 日常故障解决方案	 PAGEREF _Toc1604 \h 161
 HYPERLINK \l _Toc4123 8.2.2 垃圾数据清理服务	 PAGEREF _Toc4123 \h 162
 HYPERLINK \l _Toc1584 8.2.3 功能持续完善和BUG修复服务	 PAGEREF _Toc1584 \h 162
 HYPERLINK \l _Toc4751 8.2.4 性能调整优化服务	 PAGEREF _Toc4751 \h 162
 HYPERLINK \L _TOC25931 第9章 项目实施计划	 PAGEREF _TOC25931 \H 164
 HYPERLINK \L _TOC31357 9.1 项目整体实施进度计划	 PAGEREF _TOC31357 \H 164
 HYPERLINK \l _Toc29668 9.1.1 第一期建设实施计划（180个自然日）	 PAGEREF _Toc29668 \h 164
 HYPERLINK \l _Toc31019 9.1.2 第二期建设实施计划（180个自然日）	 PAGEREF _Toc31019 \h 164
 HYPERLINK \L _TOC32659 9.2 项目立项阶段	 PAGEREF _TOC32659 \H 165
 HYPERLINK \L _TOC10637 9.3 需求分析阶段	 PAGEREF _TOC10637 \H 165
 HYPERLINK \l _Toc13369 9.3.1 建立中医特色的系统原型与需求确认机制	 PAGEREF _Toc13369 \h 165
 HYPERLINK \l _Toc31194 9.3.2 制定精细化的项目实施计划	 PAGEREF _Toc31194 \h 166
 HYPERLINK \l _Toc14777 9.3.3 制定符合中医业务逻辑的验收测试方案	 PAGEREF _Toc14777 \h 166
 HYPERLINK \L _TOC17094 9.4 功能设计阶段	 PAGEREF _TOC17094 \H 166
 HYPERLINK \l _Toc7378 9.4.1 基于中医特色的功能设计与需求映射	 PAGEREF _Toc7378 \h 166
 HYPERLINK \l _Toc392 9.4.2 多维度的功能设计评审机制	 PAGEREF _Toc392 \h 167
 HYPERLINK \l _Toc17287 9.4.3 科学合理的测试计划发布	 PAGEREF _Toc17287 \h 167
 HYPERLINK \l _Toc4886 9.4.4 集成测试方案的定制化制定	 PAGEREF _Toc4886 \h 167
 HYPERLINK \L _TOC29168 第10章 投资估算与设备清单	 PAGEREF _TOC29168 \H 169
 HYPERLINK \L _TOC576 10.1 投资估算明细	 PAGEREF _TOC576 \H 169
 HYPERLINK \L _TOC19051 10.2 设备参考清单	 PAGEREF _TOC19051 \H 169
 HYPERLINK \l _Toc18312 10.2.1 AI智算平台配置清单	 PAGEREF _Toc18312 \h 169
 HYPERLINK \l _Toc19592 10.2.2 智能体开发与应用清单	 PAGEREF _Toc19592 \h 178
 HYPERLINK \l _Toc4338 10.2.3 数据洞察分析清单	 PAGEREF _Toc4338 \h 187
 HYPERLINK \l _Toc12327 10.2.4 互联网医院门户清单	 PAGEREF _Toc12327 \h 190
 HYPERLINK \l _Toc6432 10.2.5 运营服务内容清单	 PAGEREF _Toc6432 \h 214
 HYPERLINK \l _Toc9881 10.2.6 网络安全服务清单（已补充）	 PAGEREF _Toc9881 \h 216
 HYPERLINK \L _TOC4211 10.3 编码开发阶段	 PAGEREF _TOC4211 \H 218
 HYPERLINK \l _Toc6601 10.3.1 中医特色系统的模块化编码开发	 PAGEREF _Toc6601 \h 218
 HYPERLINK \l _Toc28678 10.3.2 单元测试保障编码质量	 PAGEREF _Toc28678 \h 218
 HYPERLINK \l _Toc444 10.3.3 多维度的代码评审机制	 PAGEREF _Toc444 \h 219
 HYPERLINK \l _Toc3525 10.3.4 编码过程的质量控制与合规性评估	 PAGEREF _Toc3525 \h 219
 HYPERLINK \L _TOC27358 10.4 集成测试阶段	 PAGEREF _TOC27358 \H 219
 HYPERLINK \l _Toc16872 10.4.1 中医业务全流程集成测试	 PAGEREF _Toc16872 \h 219
 HYPERLINK \l _Toc23549 10.4.2 系统部署与文档移交	 PAGEREF _Toc23549 \h 220
 HYPERLINK \l _Toc12697 10.4.3 项目总结与过程改进	 PAGEREF _Toc12697 \h 220
 HYPERLINK \L _TOC227 10.5 风险识别阶段	 PAGEREF _TOC227 \H 220
 HYPERLINK \l _Toc12185 10.5.1 技术风险	 PAGEREF _Toc12185 \h 220
 HYPERLINK \l _Toc26345 10.5.2 客户相关风险	 PAGEREF _Toc26345 \h 221
 HYPERLINK \l _Toc27410 10.5.3 风险缓解、监控和管理机制	 PAGEREF _Toc27410 \h 221
 HYPERLINK \l _Toc17148 10.5.4 项目风险应对措施	 PAGEREF _Toc17148 \h 221
 HYPERLINK \L _TOC9726 10.6 验收阶段	 PAGEREF _TOC9726 \H 222
 HYPERLINK \l _Toc13544 10.6.1 验收准备	 PAGEREF _Toc13544 \h 222
 HYPERLINK \l _Toc14284 10.6.2 验收组织	 PAGEREF _Toc14284 \h 223
 HYPERLINK \l _Toc23017 10.6.3 验收结论与交付	 PAGEREF _Toc23017 \h 223
 HYPERLINK \L _TOC1578 第11章 风险及预期成效	 PAGEREF _TOC1578 \H 224
 HYPERLINK \L _TOC20685 11.1 风险分析及对策	 PAGEREF _TOC20685 \H 224
 HYPERLINK \l _Toc11549 11.1.1 风险识别分析	 PAGEREF _Toc11549 \h 224
 HYPERLINK \l _Toc9269 11.1.2 风险对策	 PAGEREF _Toc9269 \h 225
 HYPERLINK \L _TOC6344 11.2 预期成效	 PAGEREF _TOC6344 \H 227
 HYPERLINK \l _Toc29612 11.2.1 患者就医体验全面升级	 PAGEREF _Toc29612 \h 228
 HYPERLINK \l _Toc9882 11.2.2 医护服务效率显著提升	 PAGEREF _Toc9882 \h 228
 HYPERLINK \l _Toc15024 11.2.3 医院运营管理提质增效	 PAGEREF _Toc15024 \h 228
 HYPERLINK \l _Toc20665 11.2.4 中医特色服务创新发展	 PAGEREF _Toc20665 \h 229
 HYPERLINK \l _Toc19691 11.2.5 科研教学能力跨越式提升	 PAGEREF _Toc19691 \h 229
项目概述
项目名称
成都中医药大学附属医院（四川省中医医院）智能门户型互联网医院项目
项目单位及负责人
项目建设单位：成都中医药大学附属医院（四川省中医医院）
单位负责人：xx
项目负责人：xx
建设方案编制单位
成都中医药大学附属医院（四川省中医医院）
建设方案编制依据
政策要求：响应国家与地方战略部署，推动医疗服务数字化转型
国家层面政策驱动
国务院《关于促进 “互联网 + 医疗健康” 发展的意见》（国办发〔2018〕26 号）明确要求 “构建覆盖诊前、诊中、诊后的线上线下一体化医疗服务模式”，支持互联网医院开展常见病、慢性病复诊及电子处方流转等服务。《关于推动公立医院高质量发展的意见》提出 “强化信息化支撑作用”，要求通过智慧医疗、智慧服务和智慧管理 “三位一体” 建设，提升服务效率。国家卫健委《医院智慧服务分级评估标准体系（试行）》进一步明确 3 级以上智慧服务需 “联通医院内外服务”，项目建设符合 4S 体系中患者全流程服务数字化要求。
中医药行业政策导向
《四川省 “十四五” 中医药高质量发展规划》强调 “以智慧化赋能中医药服务创新升级”，要求构建 “数智融合、全链贯通” 的智慧中医医院体系，研发辨证论治平台，打造覆盖全流程的互联网中医服务平台。项目通过智算平台、中医特色门户及智能体应用，直接响应四川省对中医药数字化转型与 “智能辨证 — 精准施治 — 产业联动” 发展范式的要求。
数据安全与合规要求
政策明确要求保障医疗数据安全，项目遵循《个人信息保护法》《数据安全法》，构建涵盖数据存储、传输、访问的全流程安全架构，满足三级等保要求，确保合规运营。
医院发展：破解现有痛点，推动高质量发展与中医特色化转型
业务现状与发展瓶颈
医院作为国家三级甲等中医院，现有互联网医院存在 “挂缴查” 功能与云门诊系统独立运行、中医特色服务缺失、用户体验不佳等问题。例如，系统未提供长辈版模式，缺乏中医茶饮膏方、冬病夏治等特色服务展示，无法满足患者院后健康管理需求。运营层面，当前互联网医院线上服务使用率低，品牌影响力不足。
战略升级需求
医院 “十四五” 规划明确提出建设国家区域医疗中心，项目通过 “1 个算力底座 + 1 个互联网医院门户 + N 个智能体应用” 架构，可整合中医特色资源，打造 “名医工作室”“中医健康商城” 等 IP，推动 “治未病” 服务与临床诊疗融合，形成 “中医特色 + AI 技术” 的差异化竞争力。
资源整合与效率提升
项目通过接口深度利旧、业务流程优化，可整合 HIS、LIS 等现有系统，实现数据互通与流程自动化。

患者服务：满足多元化需求，构建以患者为中心的服务生态
就医体验优化需求
患者期待突破时空限制，获得便捷的线上服务。现有系统无法支持跨机构检查报告查询、电子健康档案查看，
全生命周期健康管理需求
慢性病患者、亚健康人群对中医治未病、健康干预需求强烈。项目通过体质辨识、专病全病程管理，为患者提供个性化养生方案、用药提醒及康复指导。例如，糖尿病患者可通过系统上传血糖数据。
特殊群体服务需求
针对老年患者，项目设计适老化界面、语音导航及亲属代办功能，提升操作便捷性；针对外地患者，通过远程诊疗、云药房配送解决地域限制问题，预计年服务跨区域患者20万人次以上。
项目建设目标、内容、建设周期
项目建设目标
优化门诊结构，通过开辟“互联网+医疗健康”新模式，对院内服务进行标准化升级，在医院现有信息化平台基础上，重塑服务流程，打造线上、线下同步服务平台，提升医院服务效率，为患者提供更好的就医体验。利用互联网技术完善和补充传统医院的流程卡点和断点，为患者提供延续性服务，通过全生命周期健康管理，满足多场景应用需求，构建“以患者为中心”的服务生态。同时，通过本项目的建设，推进医院达到四川省智慧医院三星服务水平，以评促建，打造全国中医智慧服务标杆，实现年互联网医院线上问诊量100万人次。具体目标如下：
建设中医特色智慧医院+互联网医院门户
本次项目的核心目标在于构建一个融合中医特色元素的互联网医院统一平台，整合各类中医资源，打破地域和时间的限制，为广大患者提供一站式、全方位的中医服务。
互联网医院统一门户平台将广泛而全面地覆盖中医多模态内容与权威发布内容，涵盖中医经典文献、名医经验、临床案例等多种形式，确保信息的准确性和权威性。同时，平台将打造专题专区运营活动，针对不同疾病和健康需求，提供个性化的中医特色服务能力。具体包括但不限于中医在线诊疗服务，通过视频、文字等多种方式实现远程诊疗；中医健康管理服务，提供个性化的健康评估和调理方案；中医健康宣传教育，普及中医养生知识，提升公众健康素养；中医饮食调养指导，根据个体体质提供科学的饮食建议；中医延续性护理服务，为患者提供持续的健康跟踪和护理支持等多个至关重要的领域。
互联网医院统一门户平台从多个维度和层面全方位满足广大患者和治未病人群对于中医服务的多元化、个性化需求，无论是日常保健还是疾病治疗，都能在平台上找到适合自己的解决方案，从而有效提升患者在就医过程中的整体体验感和满意度。通过这一系列切实有效的举措，我们不仅能够更好地传承和发扬中医的精髓与智慧，让更多人了解和认可中医的价值，还能为广大患者提供更加便捷、高效、全面的中医健康服务，真正实现中医与现代科技的有机结合，推动中医事业迈向新的发展阶段，为健康中国建设贡献力量。
覆盖线上线下一体化就医服务
本次项目旨在构建一个融合中医特色的互联网医院统一门户，实现线上医疗服务与传统线下医疗服务的有效整合，构建起一种线上线下一体化的全新医疗服务模式。这种模式不仅能够为患者带来更加便捷和高效的医疗服务体验，使得患者无论身处何地都能享受到及时的专业医疗咨询和治疗建议，还能进一步推动医院在中药方剂领域的个性化定制服务与配送体系的发展。具体而言，医院可以根据患者的具体病情和体质，量身定制最适合的中药方剂，并通过高效的配送系统，确保患者能够及时获取到这些个性化的中药方剂，从而有效提升治疗效果，促进患者的康复进程。
此外，线上线下一体化就医服务还将促进医疗资源的优化配置。线上平台可以实时收集和分析患者的健康数据，为医生提供更加全面和准确的诊断依据，从而提高诊断的准确性和治疗的有效性。而线下医院则可以根据线上平台的数据反馈，合理调整医疗资源的分配，确保医疗服务的及时性和可及性。
打造名医工作室及中医健康商城
本次项目旨在打造一个融合中医特色的互联网医院统一门户，重点构建“名医工作室”，利用院内医疗资源建立一支具有代表性的名医队伍。通过整合人工智能、大数据等现代信息技术，平台将实现从“疾病治疗”向“健康管理”的转变。
名医工作室以及大健康概念不仅是对传统医疗服务模式的重要补充，更是医疗业务向预防性、个性化和全生命周期管理延伸的关键载体。通过信息化手段强化“防-筛-管-治”一体化服务体系，名医工作室服务将成为未来医院业务发展的重要增长点，并为提升公立中医医院在多级区域的发展提供有力支撑。
一是强化中医体质辨识服务，利用先进的AI技术和大数据分析手段，平台将实现用户中医体质的智能识别与分类。通过采集用户的生理特征、生活习惯、心理状态等多维度数据，建立科学的体质辨识模型，帮助用户深入了解自身体质特点和健康状况。该服务不仅可作为个性化的养生保健方案制定依据，也为后续的慢病筛查、干预建议和疾病预警提供了精准的数据基础，形成“辨体-干预-随访”的闭环管理机制，有效对接医院的慢病管理和亚健康门诊业务。
二是提供全面的健康风险评估，基于用户的个人健康数据、家族病史、生活方式等信息，平台将构建动态健康画像，开展多维度的健康风险评估服务。通过对潜在疾病风险进行量化分析，帮助用户及时发现早期信号并采取相应干预措施。这一功能将与医院体检中心、家庭医生签约服务形成联动，构建起“风险评估—早期干预—跟踪随访”的全过程健康管理体系，推动医疗服务由“被动诊疗”向“主动预防”转型。
三是加强疾病预防知识的普及教育，平台将定期发布权威、系统的中医养生知识和疾病预防指南，通过图文、视频、互动问答等多种形式，向用户普及中医“未病先防、既病防变、瘥后防复”的核心理念和实用方法。这种科普教育不仅是提升公众健康素养的重要手段，也将引导更多人群参与医院的健康咨询、中医调养等特色服务，促进治未病服务与医院现有医疗资源的深度融合，拓展医院的服务边界和服务价值。
四是创建中医相关健康商城，利用中医专业知识，进行知识成果转化，转化标的包括但不限于专利技术、中医健康商品（如中医茶饮、养生汤方、中医日化、养生汤方），在中医看诊的同时，介入大健康生活领域，拓展公立医院发展渠道，进行多元化的医疗行业覆盖。
发展中医特色的智能化全病程管理
本次项目建设旨在构建一个融合中医特色的互联网医院统一门户，并基于门户发展中医特色的智能化专病/慢病全病程管理系统。针对慢性疾病或特定疾病，积极推行一种以“医疗+健康”为核心的一体化服务路径。在这一路径中，将全面整合专科医生、中医科医生、护士、健康管理师等多方专业人员的团队资源与服务能力，形成一个协同高效的医疗服务体系。在此基础上，构建了一套以专病分组路径化管理为坚实基础，以疗效管理为核心理念的全病程管理模式。在这一模式下，专科医生与健康管理师将共同赋能，通过全病程的主动干预和管理，确保患者在整个治疗过程中得到全方位、个性化的医疗服务，从而有效提升治疗效果和患者的生活质量。
提升中医科研能力与中医教学水平
借助互联网医院统一门户所具备的独特优势，能够以更加高效、便捷的方式收集和整理海量的临床数据，这些数据不仅全面而且精准，为中医科研工作的深入开展提供了强有力的数据支持和保障。与此同时，互联网医院还为中医教学领域开辟了全新的途径和广阔的平台，通过线上教学、远程指导等多种创新方式，极大地提升了中医教学的整体质量和实际效果，从而有助于培养出更多理论基础扎实、实践能力突出的优秀中医人才，为中医药事业的传承与发展注入新的活力。
项目建设内容
本项目响应国家 “互联网 + 医疗健康” 及四川省中医药数字化转型要求，旨在构建 “1 算力底座 + 1 门户 + N 智能体应用” 架构。医院现有系统存在功能分散、中医特色不足、患者管理不高效等痛点，项目通过 AI 智算平台、中医特色门户及全病程管理，可优化就医流程。
项目建设周期与地点
项目建设周期：两期建设，两期建设实施计划工期各为180个自然日。
项目建设地点：成都中医药大学附属医院（四川省中医医院）。
项目总投资及资金来源
项目总投资：软硬件及运营服务投资估算共计约人民币1430万元。
资金来源：银医资金。
详见《“成都中医药大学附属医院（四川省中医医院）智能门户型互联网医院项目投资估算表》。
经济与社会效益
经济效益
直接成本节约
人力成本优化：通过“互联网医院 + AI 智能体”，减少前台咨询、病历录入等重复性工作，预计可节省 20% 医护人力投入，年节约人力成本约 300 万元。
运营成本降低：线上复诊、云药房配送减少患者到院频次，预计降低门诊耗材消耗 15%、药品库存成本 10%，年节约运营成本约 150 万元。
资源利用率提升：AI 智算平台实现算力资源动态调配，本地与云端算力结合可降低 30% 硬件采购成本，预计硬件投资回收期缩短至 3 年。
收入增长潜力
互联网服务拓展收益：实现互联网医院线上问诊100万年问诊量，依托平台提供中医健康商城、名医工作室等增值服务，预计三年之后每年达成5亿元收入。
社会效益
患者服务优化
就医效率提升：线上预约挂号、AI 分诊导诊使平均候诊时间从 60 分钟缩短至 20 分钟，年惠及患者 100 万人次，减少交通及时间成本约 6000万元。
可及性改善：远程诊疗覆盖 30 + 国家和地区，为海外侨胞、偏远地区患者提供年均 5000 例中医服务，体现中医药国际化公益价值。
适老化服务：老年版界面、亲属代办功能覆盖 50 岁以上患者，预计提升老年患者线上服务使用率至 40%。
2．医疗资源均衡
分级诊疗推动：通过 “互联网 + 专病管理” 将糖尿病、不孕症等优势病种的复诊患者下沉至基层，年分流三甲医院门诊量 10%，缓解三甲医院压力。
中医特色推广：AI 体质辨识、治未病服务年覆盖亚健康人群 10 万人次，推动 “未病先防” 理念普及，降低慢性病发病率 15%。

主要结论与建议
本项目可提高成都中医药大学附属医院（四川省中医院）信息化水平、数字化水平、医疗服务能力、医疗服务规范性、医疗服务效率、协作能力和水平、监测感知能力、群众就医感受，同时可以降低建设、运维、运营、行政管理的成本难度，更是以信息化为抓手，促进中医药行业创新发展的“重要革命”。项目深度响应 “健康中国 2030” 及中医药数字化政策，符合国家和省委、省政府的工作要求，是关系国计民生的便民服务工程。
项目建成后，可打造全国 “AI + 中医” 智慧服务标杆，形成可复制的互联网医院标准化范式，为分级诊疗与智慧医疗政策落地提供实践样本。医院通过数字化转型实现运营模式革新，以数据驱动优化资源配置，构建 “线上线下一体化” 服务闭环，打造川派中医特色品牌矩阵，提升区域医疗竞争力与行业话语权。聚焦患者全生命周期健康需求，通过 AI 赋能重构就医流程，实现中医诊疗 “精准化、便捷化、个性化”，创新 “治未病” 服务模式，推动医疗服务从疾病治疗向健康管理转型，切实提升群众就医获得感与中医服务可及性。
本项目的建设对于医院的发展是必要的，并且在经济效益、社会效益、技术保证、配套资源落实、建设条件具备、资金落实等方面具备可行性。
本项目无论从基础条件、技术选型、经济等方面，都具备较强的可行性，且项目建设单位具备较强的信息化建设综合实力和经验，已具备完成本项目的基础条件。
通过对实际情况的深入调查，并广泛征求四川省卫健委、四川中医药管理局等相关部门的意见，在充分分析、研究和论证建设方案的基础上，认为本项目是可行的。
该项目符合国家政策和成都中医药大学附属医院（四川省中医院）信息化建设规划，建设目标明确、建设内容清晰、技术成熟、方案合理、建设可行，建议尽快批准本项目立项，使其尽早投入建设。
项目单位概况
项目建设单位与职能
成都中医药大学附属医院（四川省中医医院）创建于1957年，是新中国最早成立的四所高等中医药院校附属医院之一，也是国家“双一流”学科建设高校附属医院。经过67年建设发展，现已成为集医疗、教学、科研、预防、保健、养生康复为一体的三级甲等中医院，是国家中医临床研究(糖尿病)基地、国家中药临床试验研究（GCP）中心、国家中医药高层次人才培养基地、国家首批中医（中医全科）住院医师规范化培训基地、国家中医药国际合作基地、国家中医临床教学培训示范中心、全国护理科普教育基地等。十四五以来，又先后获批国家中医药传承创新中心、国家中医疫病防治基地、被纳入国家医学中心（中医类）辅导类创建单位，成都中医药大学附属医院（四川省中医医院）获批第五批国家区域医疗中心，国家三级公立医院绩效考核医院连续三次排名全国中医综合医院前八。
医院编制床位3000张，现有临床科室45个，医技科室10个，专科门诊47个，专病门诊64个。年门急诊服务量为370余万人次，住院量9万余人次。
医院遵循中医药发展规律，注重突出中医特色。拥有5个国家区域（中医）专科诊疗中心，6个国家临床重点专科（中医专业），13个国家中医药管理局重点专科，10个国家中医优势专科建设单位，是全国中医眼病医疗中心、全国中医急症医疗中心、国家中医药管理局中医中西医结合急诊临床基地和感染病临床基地等。同时，医院以重点专科为依托，在省内外牵头建立了二十余个专科/专病联盟，通过以点带面，以优带全，整体推进，形成同质化中医药专科专病服务布局。医院建成了具有日配方30000剂中药服务能力的中药房，中药饮片质量控制在全国中医系统达到领先水平。同时积极推进制剂研发，目前医院制剂注册或备案制剂103种，纳入四川省中医药管理局、四川省药品监督管理局联合公布《四川省医疗机构中药制剂调剂品种目录》（第一批）的制剂51个，在全省医疗机构调剂使用。
随着中医药“一带一路”国际化传播与推广的不断深入，医院对外交流与合作取得了进一步发展。先后为30多个国家和地区培养了5000多名中医药、针灸、推拿专业人才。接待了来自美国、德国、葡萄牙等30多个国家和地区的来访者5000余人次。不断推进中医药国际医疗合作，获批成立国家中医药管理局中医药国际合作基地（四川），建设“天府云医-海外惠侨远程医疗站（西班牙）、（希腊）”。近年分别在黑山共和国首都波德哥里察、德国巴伐利亚州里德林市、在白俄罗斯戈梅利州成立分院。2017年中国－黑山中医药中心挂牌运营，成为欧洲第二所中医药中心。
目前，医院已成为我国中西部地区中医药特色突出、临床学科门类齐全、自主创新能力较强、区域影响力明显、综合实力雄厚的中医医疗中心、科教中心和治未病中心，先后获得全国示范中医院、全国卫生系统行业作风建设先进集体、全国医药卫生系统先进集体、全国中医药应急工作先进集体、全国公立医院党建示范医院等一系列荣誉。
本次项目借助移动互联网等新技术拓展医疗服务空间，支持医院精细化高质量发展，构建院内院外一体，覆盖诊前、诊中、诊后的新型互联网平台。
项目实施单位与职能
本项目建设实施机构为成都中医药大学附属医院（四川省中医医院）门诊部和人工智能与信息化部。主要职责包括：
组织拟订全院卫生健康事业发展中长期规划；
指导全院卫生健康服务体系及信息化建设；
拟订全院大型医用装备配置管理办法和标准并组织实施；
负责“成都中医药大学附属医院（四川省中医医院）智能门户型互联网医院项目”相关文档及方案的编制，配合有关部门进行项目可行性论证和技术评估等立项的准备工作；
建立健全相关制度和标准规范，强化制度、标准规范的使用和落实；
组织开发建设；组织开发建设负责“成都中医药大学附属医院（四川省中医医院）智能门户型互联网医院项目”相关文档及方案的编制，配合有关部门进行项目可行性论证和技术评估等立项的准备工作；
建设必要性
项目建设背景
政策背景
近几年，国家及四川省陆续出台了一系列政策文件，涵盖战略规划、服务规范、技术标准、数据安全及医保支付等多个层次，推动互联网医院和患者全生命周期的智慧服务的快速发展。
《关于促进“互联网+医疗健康”发展的意见》（国办发〔2018〕26号）
为深入贯彻落实习近平新时代中国特色社会主义思想和党的十九大精神，推进实施健康中国战略，提升医疗卫生现代化管理水平，优化资源配置，创新服务模式，提高服务效率，降低服务成本，满足人民群众日益增长的医疗卫生健康需求，根据《“健康中国2030”规划纲要》和《国务院关于积极推进“互联网+”行动的指导意见》（国发〔2015〕40号），经国务院批准，2018年4月28日国务院办公厅发布《关于促进“互联网+医疗健康”发展的意见》（国办发〔2018〕26号）。
《意见》主要有三方面内容：一是健全“互联网+医疗健康”服务体系。从医疗、公共卫生、家庭医生签约、药品供应保障、医保结算、医学教育和科普、人工智能应用等方面推动互联网与医疗健康服务相融合，涵盖了医疗、医药、医保“三医联动”诸多方面；二是完善“互联网+医疗健康”的支撑体系。为及时制定完善相关配套政策、加快实现医疗健康信息互通共享、建立健全“互联网+医疗健康”标准体系，提高医院管理和便民服务水平、提升医疗机构基础设施保障能力等方面提出了有关举措；三是加强行业监管和安全保障，对强化医疗质量监管和保障数据安全作出明确规定。
在发展“互联网+”医疗服务方面，《意见》鼓励医疗机构应用互联网等信息技术拓展医疗服务空间和内容，构建覆盖诊前、诊中、诊后的线上线下一体化医疗服务模式。允许依托医疗机构发展互联网医院，医疗机构可以使用互联网医院作为第二名称，在实体医院基础上，运用互联网技术提供安全适宜的医疗服务，允许在线开展部分常见病、慢性病复诊活动。医生在掌握患者病历资料后，允许在线开具部分常见病、慢性病处方。支持远程医疗、电子处方流转、医保在线结算等创新服务，推动家庭医生签约智能化。
《意见》提出的一系列政策措施，明确了支持“互联网+医疗健康”发展的鲜明态度，突出了鼓励创新、包容审慎的政策导向，明确了融合发展的重点领域和支撑体系，划出了监管和安全底线。政策出台有利于深化“放管服”和供给侧结构性改革，缓解医疗卫生事业发展不平衡不充分的矛盾，满足人民群众日益增长的多层次多样化医疗健康需求。
《互联网诊疗管理办法（试行）等3个文件》
为贯彻落实《国务院办公厅关于促进“互联网+医疗健康”发展的意见》有关要求，进一步规范互联网诊疗行为，发挥远程医疗服务积极作用，提高医疗服务效率，保证医疗质量和医疗安全，国家卫生健康委员会和国家中医药管理局于2018年7月17日发布了《关于印发互联网诊疗管理办法（试行）等3个文件的通知》。通知附件包含《互联网诊疗管理办法》《互联网医院管理办法》《远程医疗服务管理规范》，对互联网医院、诊疗以及远程医疗服务的具体运营提出了指导性的要求：包括从互联网医院的准入、诊疗行为的范围、互联网监督平台的建设、科室设置、人员要求、技术要求、诊疗行为、电子病历、在线处方、信息安全和患者隐私保护、远程医疗的服务流程等方面。
这一系列文件的发布对于互联网医院的发展有着里程碑式的意义，使互联网医院形成一个完整的服务体系成为可能，一系列的互联网医疗服务将会围绕互联网医院的整体建设来展开。
《关于推动公立医院高质量发展》
国务院办公厅印发的《关于推动 HYPERLINK "https://zk.cn-healthcare.com/doc-show-59649.html" 公立医院高质量发展的意见（国办发〔2021〕18号）》（以下简称《意见》），提出以习近平新时代中国特色社会主义思想为指导，以人民健康为中心，以建立健全现代医院管理制度为目标，强化体系创新、技术创新、模式创新、管理创新，加快优质医疗资源扩容和区域均衡布局，力争通过5年努力，公立医院发展方式从规模扩张转向提质增效，运行模式从粗放管理转向精细化管理，资源配置从注重物质要素转向更加注重人才技术要素，为更好提供优质高效医疗卫生服务、防范化解重大疫情和突发公共卫生风险、建设健康中国提供有力支撑。
在宏观层面，通过打造国家级和省级高水平医院、发挥公立医院在城市医疗集团中的牵头作用、发挥县级医院在县域医共体中的龙头作用、建立健全分级分层分流的重大疫情救治体系等措施，构建公立医院高质量发展新体系；在微观层面，通过加强临床专科建设、推进医学技术创新、推进医疗服务模式创新、强化信息化支撑作用，引领公立医院高质量发展新趋势；通过健全运营管理体系、加强全面预算管理、健全绩效评价机制，提升公立医院高质量发展新效能；通过改革人事薪酬制度、健全医务人员培养评价制度、健全医务人员培养评价制度、深化医疗服务价格改革、深化医保支付方式改革等手段，激活公立医院高质量发展新动力；通过全面执行和落实党委领导下的院长负责制、加强公立医院领导班子和干部人才队伍建设、全面提升公立医院党组织和党员队伍建设质量、落实公立医院党建工作责任等措施，坚持和加强党对公立医院的全面领导。
《意见》始终把提高人民群众看病就医的获得感作为公立医院高质量发展的重要目标之一，提出了一系列的工作安排。首先，建立高质量的分级诊疗体系，优化区域城乡布局，做到大病不出省，一般病在市县解决，日常疾病在基层解决。其次，创新医疗服务模式。在公立医院高质量发展阶段，继续大力推行多学科诊疗、 HYPERLINK "http://www.cn-healthcare.com/freezingapi/wapi/cloudwords/getwordinfo?wordid=29" 日间手术、责任制整体护理、延续护理等服务模式，并大力发展 HYPERLINK "http://www.cn-healthcare.com/freezingapi/wapi/cloudwords/getwordinfo?wordid=45" 远程医疗和互联网诊疗，进一步完善预约诊疗、在线支付等惠民举措，通过建设智慧医疗、智慧服务和智慧管理“三位一体”的智慧医院，采用信息化的手段，来提高医疗服务的效率，让人民群众能够更加便捷、高效地享受到优质医疗服务。
《四川省“十四五”中医药高质量发展规划》
《四川省“十四五”中医药高质量发展规划》明确提出以智慧化赋能中医药服务创新升级，通过构建“数智融合、全链贯通”的新型智慧中医医院体系，全面推进中医药服务数字化、精准化转型。规划聚焦中医特色与前沿技术深度融合，依托省级中医药大数据中心建设，系统集成川派中医经典诊疗经验与人工智能技术，研发辨证论治平台，实现舌诊、脉诊等传统四诊技术的数据化解析与智能辅助决策；打造覆盖“诊前-诊中-诊后”全流程的互联网中医服务平台，贯通省市县三级中医医疗机构远程会诊网络，推动中药智慧药房建设，实现处方审核、代煎配送、质量追溯一站式服务，并创新“区块链+中医药”质量管控模式，对川产道地药材从种植加工到临床应用的全程进行链上存证监管。同步推进电子病历五级、智慧服务三级标准化建设，构建中医优势病种临床科研大数据平台（如内分泌、针灸等特色专科），强化中医药数据与全民健康信息平台互联互通，培育中医AI辅助诊疗、川派名老中医经验数字化传承等示范工程，力争成为具有区域乃至全国影响力的智慧中医医院标杆，形成“智能辨证—精准施治—产业联动”的中医药高质量发展新范式。
规范和指引
《全国医院信息化建设标准与规范》（2018年）
2018年4月国家卫生健康委员会针对目前医院信息化建设现状，着眼未来 5～10年全国医院信息化应用发展要求，针对二级医院、三级乙等医院和三级甲等医院的临床业务、医院管理等工作发布了《全国医院信息化建设标准与规范》（以下简称《建设标准》），《建设标准》覆盖医院信息化建设的主要业务和建设要求，从软硬件建设、安全保障、新兴技术应用等方面规范了医院信息化建设的主要内容和要求，分为业务应用、信息平台、基础设施、安全防护、新兴技术等5章22类262项具体内容。通过便民服务、医疗服务、医疗协同、人工智能等功能模块明确了互联网医院和患者智慧服务信息化建设的具体内容和要求。
《医院智慧服务分级评估标准体系（试行）》
2019年3月19日，国家卫健委组织发布了《医院智慧服务分级评估标准体系（试行）》（Smart Service Scoring System，4S）评价体系。评价体系指出医院智慧服务对于医院的重要性，明确医院智慧服务是智慧医院建设的重要内容，指明了医院针对患者的医疗服务需要，应用信息技术改善患者就医体验，加强患者信息互联共享，提升医疗服务智慧化水平的新时代服务模式：
评价体系指出了医院智慧服务建设的目的，建立医院智慧服务分级评估标准体系（Smart Service Scoring System，4S），旨在指导医院以问题和需求为导向持续加强信息化建设、提供智慧服务，为进一步建立智慧医院奠定基础。
评价体系也规定了医院智慧服务的建设内容，按照患者诊前、诊中、诊后各环节应涵盖的基本服务内容，结合医院信息化建设和互联网环境，确定5个类别共17个评估项目，电子病历、医院运营、教学、科研等信息化建设情况不在本评估范围内。
围绕17个评估项目分别对医院智慧服务信息系统的功能、有效应用范围进行评分。功能评估按照实现的功能等级获得等级评分，有效应用范围评估按照实际应用情况获得相应的比例系数评分。将两个得分相乘，得到此评估项目的综合评分。
单个项目综合评分=功能评分×有效应用范围评分
具体方法是按照总分、基本项目完成情况、选择项目完成情况得到评估结果，分为0—5级共六个等级。
医院智慧服务分级评估基本要求：
等级
内容
基本项目数（项）
选择项目数（项）
最低总分（分）
0级
医院没有或极少应用信息化手段为患者提供服务
——
——
——
1级
医院应用信息化手段为门急诊或住院患者提供部分服务
4
8/13
10
2级
医院内部的智慧服务初步建立
6
6/11
20
3级
联通医院内外的智慧服务初步建立
8
4/9
30
4级
医院智慧服务基本建立
9
3/8
41
5级
基于医院的智慧医疗健康服务基本建立
9
3/8
51

医院智慧服务分级评估项目：
序号
类别
业务项目
应用评估
1
诊前服务
诊疗预约
应用电子系统预约的人次数占总预约人次数比例
2
急救衔接
具备急救衔接机制和技术手段并有效应用
3
转诊服务
应用信息系统转诊人次数占总转诊人次数比例
4
诊中服务
信息推送
应用信息技术开展信息推送服务
5
标识与导航
具备院内导航系统
6
患者便利保障服务
具备患者便利保障系统并有应用
7
诊后服务
患者反馈
电子调查人次占全部调查人次比例
8
患者管理
应用电子随诊记录的随诊患者人次数占总随诊患者人次比例
9
药品调剂与配送
具有药品调剂与配送服务系统并有配送应用
10
家庭服务
具有电子记录的签约患者服务人次占总签约患者服务人次比例
11
基层医师指导
应用信息系统开展基层医师指导
12
全程服务
费用支付
具备电子支付系统功能并有应用
13
智能导医
有智能导医系统功能并有应用
14
健康宣教
有健康宣教系统并有应用
15
远程医疗
具备远程医疗功能并有应用
16
基础与安全
安全管理
应用身份认证的系统占全部系统比例
17
服务监督
具有服务监督机制并有监督记录
新技术支撑
2025年以来，随着在医疗人工智能领域的发展，DeepSeek通过构建多维度认知计算框架，将5G网络承载的实时医学影像、动态生命体征与电子病历深度耦合，形成立体化诊疗决策系统：其独创的流式处理引擎，可在急救车抵达医院前完成对患者病情的全息解析，为急诊医生提供三维可视化诊断支持；而火山引擎开发的医疗知识蒸馏体系，将顶尖专家的临床经验转化为可动态演化的数字诊疗逻辑，能精准复现三甲医院的诊断思维。二者均展现出突破性技术特质——前者在急诊场景中实现了救护端与诊疗端的认知同步，后者构建了医疗知识跨地域流动的智能通道。这种技术突破不仅重新定义了医疗数据的价值维度，更通过5G与AI的共振效应，正在塑造“数据即诊疗、连接即服务”的新型医疗生态。
现状分析
现状及痛点
业务现状
成都中医药大学附属医院（四川省中医医院）在互联网医院的建设方面已取得显著成就，提供了预约挂号、门诊缴费、报告查询、住院服务等多项服务。然而，从中医药发展的视角审视，该医院在互联网医院创新应用的建设方面仍有诸多改进和完善的空间。与全国其他三甲医院在互联网医院平台建设方面的卓越表现相比，亦存在一定的差距：
现有的常规“挂缴查”功能与云门诊系统各自独立运行，彼此之间缺乏有效的整合与联动，导致用户无法在一个统一的平台上享受到一站式、便捷的线上医疗服务，这种分离的状态极大地影响了用户的操作体验和服务效率。
系统所提供的功能不仅种类不够丰富，而且在分类上显得较为混乱，缺乏明确的逻辑层次，使得用户在使用过程中难以快速找到所需服务。此外，首页的设计未能充分凸显出医院的中医专科特色以及中医药业务的独特亮点，无法给用户留下深刻的印象。
当前系统并未提供专门的长辈版模式，未能充分考虑到中老年群体在使用线上服务时的特殊需求和操作习惯，导致这部分用户在使用过程中可能会遇到诸多不便，难以顺畅地享受到线上医疗服务的便利。
系统目前无法支持用户在线查询患者在各级医疗机构所进行的全部检查报告，这一功能的缺失使得患者难以全面掌握自己的健康状况。同时，居民也无法通过该系统查看自己的电子健康档案，限制了用户对个人健康信息的获取和管理。
系统中明显缺乏对本院中医特色的展示和应用，诸如茶饮膏方、冬病夏治、穴位贴敷等具有中医特色的诊疗服务均未能在互联网诊疗平台上得到体现。这种缺失不仅使得患者在出院后的健康管理上缺少了一个重要的环节，也未能充分发挥中医在健康管理中的独特优势。
系统尚未开展诸如互联网+专病管理、居家护理等延续性的院后服务项目，这些服务的缺失使得患者在出院后难以获得持续、有效的医疗关怀和支持，影响了患者整体的治疗效果和生活质量。
运营现状
在互联网医院的日常运营和管理过程中，互联网医院在服务功能宣传方面存在显著的不足，缺乏系统化、体系化的宣传策划与内容运营策略。具体而言，主要存在以下几个方面的突出问题和挑战：
官方服务号在运营管理上缺乏系统性和体系化的规划与执行，近两年来仅仅发布了一篇宣传文章，而且这篇文章的内容质量也并不理想，未能有效吸引和留住目标用户群体，导致官方服务号的作用未能充分发挥，用户粘性较低。
医院方面没有形成定期发布各项线上服务详细指引的良好习惯，对于平台上的各项功能和服务也缺乏有针对性地宣传推广措施，导致用户对服务内容的了解程度不足，使用率长期处于较低水平，未能充分挖掘和利用互联网医院的服务潜力。
在举办各类线上线下活动时，未能充分结合各种节日和特定主题进行精心的策划和设计，活动形式显得过于单一和单调，主要还是以长时间的义诊活动为主，没有能够突出互联网医院的服务特色以及针对特定疾病的主题宣传，难以形成具有辨识度和影响力的品牌效应。
互联网医院在服务引导方面所使用的图片设计感不足，缺乏视觉吸引力和创意，难以激发用户的使用兴趣和好奇心，这在一定程度上也影响了用户对互联网医院服务的整体感知和体验，降低了用户对服务的满意度和忠诚度。
发展要求和需求
以患者为中心一体化全生命周期医疗健康管理服务需求
在现代医疗体系中，以患者为中心的全生命周期医疗健康管理服务正逐渐成为核心理念。这种服务模式强调从患者出生到终老的全程健康关怀，涵盖预防、诊断、治疗、康复等各个环节。线上线下的深度融合，为患者提供了无缝衔接的医疗服务体验。
线上平台通过电子病历、远程医疗、移动医疗应用等技术手段，优化线上线下诊疗流程，建立基于电子病历的院内外一体化医疗服务流程，使患者能够随时随地获取医疗咨询、预约挂号、在线复诊、健康监测等服务。线下实体医院则继续承担着复杂疾病的诊断和治疗任务，同时为患者提供面对面的个性化服务。
这种线上线下一体化的服务模式，不仅提高了医疗服务的可及性和便利性，还通过数据共享和协同工作，提升了医疗资源的利用效率。医院、社区卫生服务中心、家庭医生等多级医疗资源的整合，确保了患者在不同阶段都能获得最合适的医疗服务，真正实现了以患者为中心的全生命周期健康管理。
以医疗质控消息为核心的管理需求
在医院管理和运行流程中，消息通知渠道依赖人工、电话等，缺乏实时、自动、畅通的通知提醒，医疗质控消息通知未能完全留痕，响应不及时，这些弊端都会影响医疗管理和质量提升。一方面，医护人员无法及时获取患者病情变化、检查检验结果等关键信息，可能导致延误诊断和治疗，增加患者安全风险。另一方面，由于各科室之间信息传递不畅，容易出现沟通失误或重复检查，降低医疗效率，浪费医疗资源。质控部门也反映难以实时掌握医疗流程中的问题（如危急值响应、抗菌药物使用、病案质控提醒响应等），无法及时反馈和纠正，影响医疗质量的持续提升。
医院降本增效，拓宽服务渠道，业务发展和引流需求
在医疗支付制度改革和降本增效的大环境下，现代医疗模式正从“以疾病治疗为中心”向“以健康管理为中心”演进。医院需要以患者为核心，更加主动地参与到患者医疗健康管理中去，通过整合预防、诊疗、康复三大环节，构建覆盖个人健康全周期的连续性服务体系。利用互联网医院及其他智慧服务工具升级门诊服务，通过开辟“互联网+医疗健康”新模式，对院内服务进行标准化升级，依托互联网医院一体化平台重塑服务流程，构建医患协同决策机制，使医疗服务从碎片化的单次诊疗，转变为贯穿生命全程的陪伴式健康管理，最终实现医疗资源精准匹配、健康效益持续提升和医疗成本有效控制的三重目标：
在诊前预防阶段，依托智能穿戴设备和健康大数据平台，建立个性化风险评估模型，提供营养指导、运动建议等主动干预方案；
诊中诊疗环节打通线上问诊、电子病历共享、远程会诊等数字通道，与线下检查、手术、住院形成有机联动，实现“云端预诊－线下确诊－数字随访”的闭环管理；
诊后康复阶段通过物联网设备实时监测生理指标，结合AI算法动态调整康复计划，形成院外延续护理新模式。这种服务体系打破传统医疗的时空限制，借助统一数据平台实现健康档案的跨机构流转，通过智能预警系统提前管控健康风险。
需求分析
互联网医院用户需求分析
患者端应用
提供患者端和医生端的登录注册功能。
患者端输入姓名、证件号、证件类型、手机号、验证码等信息完成注册。首次注册完成后可直接登录相应的客户端使用功能，也可以修改个人资料（姓名、手机号、家庭住址等）。如果是微信公众号或者小程序，首次绑定个人信息后自动登录，无需再次输入密码。
医生端输入身份证号和密码登录系统，主要是通过医院医生APP，首次注册完成后需要绑定HIS的工号密码，用于验证医生的身份有效性。
电子健康卡：系统支持对接国家标准规范的电子健康卡，主要用于在患者注册环节的实名认证（包含常规的信息校验以及人脸识别认证）。
医生排班查询：可根据日期、科室、诊室查询院内医生的排班情况。包括传统的线下挂号排班以及互联网医院的排班。其中互联网医院排班既可以在HIS端统一安排，也支持医生在APP上利根据个人情况自助排班。
支持获取统一资源池相关号源信息并发布在患者端上，用户选择科室、医生，就诊日期后进行挂号预约，在线支付挂号费用，预约成功。未支付挂号费用的订单可以在10 分钟内进行支付，超出10分钟订单自动失效，并释放号源。
根据医院需求可设置预约退号黑名单。如半年内超过3次取消预约，则拉入黑名单。若要恢复，需至医院现场办理。支持根据病人的预约挂号信息，面向患者提供诊前、诊中、诊后的各类提醒功能。
互联网医院系统的在线挂号，跟线下挂号规则基本一致，患者选择科室、医生，根据医生排班的情况，选择在线问诊的方式（图文、语音、视频问诊）。医生接诊后，患者即可与医生在线沟通。
候诊查询：通过患者端实时查询各个分诊台的叫号信息与队列情况，让病人无需长久在候诊室等待，通过手机即可了解当前叫号情况，同时使候诊室病人得到分流，改善病人就诊体验，并支持在就诊时间前提醒患者。
门诊签到：支持分诊扫码签到，可实时查询各个分诊台的排队信息，让病人无需长久在候诊室等待，通过手机即可了解当前排队情况。
在线缴费：患者端可直接通过微信、支付宝完成费用结算，不需要患者到收费处排队结算。不论是HIS医生站开具的处方，还是互联网医院系统开具的处方，支付结果能通过接口直接回传到HIS系统当中。目前支持门诊缴费、住院预交金和医生站预约挂号。若发生异常订单，支持24小时原路退费。
在线问诊：用户通过患者端选择问诊医生，选择问诊方式（图文问诊、语音问诊、视频问诊），输入病情描述，提交问诊申请，在线支付问诊费用，等待医生接诊。医生接诊后，患者可以与医生进行文字、图片、语音或视频进行沟通交流。医生了解患者病情后，对于复诊患者在线开具检验检查申请、书写病历和开具处方。对于初诊患者医生只能开具处理建议。医生端开具处方后，用户可在线支付检验、检查费用、药品费用、处置项目费用，也可根据实际情况到医院线下支付。
系统支持三种问诊方式，包括：图文、语音和视频问诊。
系统支持未接诊订单退单，按支付路径进行退费。
系统支持医生主动结束问诊，若不主动结束，本次问诊在患者支付费用后24小时后自动结束。
问诊当前医生的问诊订单未结束时，不能发起该医师的新的问诊申请。
结束问诊后，患者可以对本次问诊进行评价。
药品配送：通过互联网医院系统开具的处方，经药师在线审核通过后，用户可选择到院自取或在线配送。如果选择在线配送，则输入用户收货地址，在线支付药品费用。可在线查看物流进度，用户在收到药品后，检查药品外包装完整性并确认收药。
体检预约：系统提供体检须知、体检流程、体检套餐介绍等，用户可在线预约体检。根据患者性别，选择体检日期、体检套餐，在线支付后，系统生成相应体检注意事项，通过平台发送给用户；用户在预约的体检日期，持有效证件和订单条码到指定预约地点进行体检。体检签到前可以退费，需到医院现场办理退费。
体检报告：支持将病人体检信息推送给患者，可以查询患者及家庭成员的所有体检报告信息。同时支持在线预览PDF格式的体检报告
检验/检查预约：支持医生通过医院HIS系统或者互联网医院系统，给患者开具相应的检验/检查项目；也支持用户自主开单，在线预约检查检验项目。支持用户选择在线支付缴费，支付成功可以预约检验/检查时间；患者在预约的时间段内，持有效证件和订单条码到医院职能科室进行签到并进行检验检查；签到前可选择退费。
查询检验/检查报告：将病人检验/检查报告信息推送给用户，用户可以查询本用户下的检验/检查报告信息。
住院预约：医生通过HIS系统或者互联网医院系统，开具入院申请单。患者选择在线预约入院时间和床位类型。线下完成入院办理后，可以在线支付住院预交金。若需取消，请患者到医院现场办理取消及退费。
费用查询：可查询个人门诊缴费信息、包含药品费用、治疗费、化验费等，并可查询费用明细。支持查询住院每日清单，查看当日住院费用及详情、总费用、入院天数等信息。
满意度调查：结合智慧医院建设指标，支持患者在线反馈就诊满意度，包括门诊满意度、住院满意度，调查内容包含：医德医风、导诊人员服务态度、挂号/收费人员服务态度、门诊医生服务态度、急诊医生服务态度、检验科人员服务态度、放射/超声/病理科人员服务态度、护士服务态度、住院环境、膳食、信息公开等。医院可自行维护问卷调查内容，统计并导出问卷调查结果。同时系统支持在业务环节中向患者推送满意度调查问卷。
服务评价：患者就诊后对专家评价，支持评分和主观文字评价，支持患者在线留言反馈和投诉；支持评分的统计，作为绩效依据。
即时查询：可查询医院实时的床位信息，查看各个科室剩余床位数；可实时查询医院药品的单价；可实时查询医院诊疗项目的价格。
医院介绍：可查看医院简介、各个科室介绍、全部医生介绍。内容由医院自行维护。
健康宣教：医院可将相关信息通过患者端发布给用户，即可提供包括关于个人、医院、科室、医生的相关信息，也可以维护各类健康宣教、用药指导等信息，方便用户了解医院动态及个人健康信息。
个人中心：可查看个人账号，维护个人基础信息；支持管理个人及家庭成员就诊卡；查询订单（问诊订单、挂号订单、缴费订单）及支付记录，维护收货地址等。也可结合智慧医院，设置各类推送权限。可查询用户关注或问诊过的医生。

医务端应用
用户登录：医务人员（医生、药师）输入账号和密码登录系统（使用注册APP时填写的身份证和密码登录）。APP会自动根据用户类型展示对应的功能模块。
在线问诊：管理在线问诊的患者，可以在线接诊、在线开放等。患者列表分为待回复、已回复、已结束三个选项卡。在线问诊24小时内有效，问诊有效期内医生患者可以相互发消息，医生可以给患者编写病历，查看线下的门诊、住院病历，开具处方、检查、检验、处置项目，查看历次的检查检验报告，历次的线上线下就诊记录，开具入院证等。医生可以根据实际情况，主动结束问诊，也可以将当前患者加入黑名单。发生患者挂错号的情况时，医生可以主动为患者退号，退号后问诊自动结束。结束问诊后医患都不能再发消息，但是医生还是可以查看当次就诊的聊天记录，就诊记录，也可以操作退号。
消息模板：医生可以根据个人习惯，自主管理常用内容的消息模板。在问诊界面时可以快速通过模板消息，将一些常用语发送给患者，提高医生操作效率。医生开具处方时，可以根据医院的实际情况，对接三方的合理用药、前置审方系统（需对接三方合理用药系统），进一步提升操作效率及安全性，减少人为操作失误的情况。
处方管理：医生管理所有在线开过的处方，可以显示处方的不同状态（新建、已审核、已驳回、已作废），可以查看处方详情，可以作废处方。也能查看线上排了互联网医院班次的药师，快速了解当前负责审核线上处方的人员安排。
处方审核：互联网药师（中药师、西药师）对医生在线开具的处方进行审核，主要是审核通过和审核不通过，审核不通过时需要填写驳回理由。
在线排班：医生、药师均可以在APP上自助排互联网医院班次，根据自己的实际情况随时随地开展互联网医院问诊服务。医生排班时，自行设置开诊时间、结束时间，坐诊时长，当次预约人数上限、坐诊科室，号别，问诊类型（图文、语音、视频）。也可维护个人擅长，方便患者了解医生情况。首次使用需要自己排一次班，后续都可以通过复制排班，快速排线上班次。
电子签章：互联网医院系统（病历编辑、开处方、审核处方）全业务流程对接了CA。医生端需要先在手机上设置CA签名，申请CA证书，完成CA的认证。
工作量统计：医生药师可以在手机上查看自己的工作量，医生端目前主要统计咨询人数和开方次数，药师端主要统计审核处方次数
问诊配置：查看医生和药师的互联网医院问诊权限配置等基础信息
黑名单管理：医生针对在线问诊的患者，可以根据实际情况对患者进行拉黑操作，也可以解除黑名单
消息通知：患者问诊后，互联网医院系统会给医生进行短信推送，APP消息提醒。医生端回复首次患者后，系统会向患者端推送一次提醒。医生开具处方后，系统会向对应的药师（中药师、西药师）推送短信提醒、APP推送。药师审核处方后（通过、驳回），系统会再通知医生，同时如果处方审核通过，系统会通知患者处方已开具。
处方流转：通过互联网医院系统开具的处方，可以直接流转到与当前医院合作的第三方外部药房，方便患者结合自身情况，到院自取药品，由医院配送药品或者由合作的第三方药房进行物流配送药品，更大程度提升患者就医便捷性。
网络切换：根据医院情况，支持内外网切换。部分功能在内网下不可使用。
工号修改：医生HIS工号发生变化时，可以修改医院医生APP绑定的工号。
指纹登录：首次账密登录后，医生可以在这里设置指纹，后续直接可以指纹登录，方便快捷（需要手机本身支持设置指纹）。
清除未读消息：清除无效的未读消息提醒，避免造成医生误会

管理人员需求分析
提升医疗机构运营效率需求：通过互联网平台整合医疗资源，优化内部管理流程，实现线上线下患者就诊流程的无缝衔接，提高床位、设备等资源的利用率。例如，将互联网平台的预约数据与线下医院的排班系统对接，合理安排医护人员工作，减少资源浪费。
医疗质量管控需求：借助平台的大数据分析和监控功能，对医生的诊疗行为、患者满意度等进行实时监测和分析，确保医疗服务质量符合标准。例如，通过分析患者对医生的评价数据，及时发现并处理可能存在的医疗服务问题，提高整体医疗质量。
拓展业务与市场竞争需求：利用互联网医院的优势，开发新的医疗服务项目，如远程医疗协同、互联网健康管理套餐等，提升医疗机构在市场中的竞争力，吸引更多患者。例如，推出针对企业员工的健康管理套餐，包括定期线上体检咨询、健康讲座等服务，拓展客户群体。
管理层行为模式对信息化支撑的要求
平台搭建与运营决策：参与互联网医院平台的规划、建设和运营决策，确定平台的功能模块、服务范围、合作模式等。例如，根据互联网平台的战略规划和市场需求，决定是否与第三方企业合作，提供线下运营服务。
医生与患者管理：对入驻平台的医生进行资质审核、排班管理、绩效评估等工作；同时关注患者的反馈和需求，制定相应的服务改进措施。例如，定期分析患者投诉数据，针对集中反映的问题调整医生培训内容或优化平台服务流程。
数据分析与业务优化：收集和分析互联网医院平台的运营数据，如患者流量、诊疗业务量、收入构成等，根据分析结果调整业务策略，优化资源配置。例如，发现某科室在互联网医院的咨询量持续增长，可适当增加该科室的医生资源和服务项目。
合作与推广活动组织：与其他医疗机构、医药企业、保险公司等开展合作洽谈，拓展业务渠道；组织互联网平台的宣传推广活动，提高平台知名度和影响力。例如，与保险公司合作推出互联网平台专属保险产品，吸引更多参保患者使用平台服务；通过举办线上义诊、健康科普活动等提升平台人气。
使用体验的影响因素
政策法规环境：医疗机构管理人员需密切关注国家和地方关于互联网医院的政策法规变化，确保平台运营合法合规。例如，政策对互联网医院的医保接入条件、数据安全管理要求等规定会直接影响平台的建设和运营策略。
技术与资金投入：互联网平台的建设和维护需要大量的技术和资金支持，管理人员要权衡投入与产出效益。例如，在选择平台技术开发方时，既要考虑技术水平，也要考虑开发成本和后续维护费用；同时，要评估平台运营后的盈利模式和投资回报周期。
行业竞争态势：分析竞争对手的互联网医院平台建设情况、服务特色、市场份额等，制定差异化的竞争策略。例如，若其他医疗机构的互联网医院主打高端专家服务，本机构可重点发展基层医疗服务的互联网延伸，突出服务的便捷性和亲民性。
互联网医院运营平台（Web）
基础数据维护：维护医院互联网系统基础参数，如物流参数、CA参数、平台参数等。
权限审核：维护互联网医院的医生、药师的处方、病历、中药、西药等操作权限。
注册用户管理：对互联网医院系统注册的患者信息进行管理，如姓名、手机号、证件号、证件类型等，也可以对患者进行注销操作。
操作留痕追溯：对互联网医院系统的日常操作，均记录了操作日志，方便追溯。同时对于医患之间产生的聊天记录，也可以随时查看，便于运营管理人员核对。
订单管理（问诊、挂号、缴费、体检）：对互联网医院系统产生的订单进行管理，核对订单信息、患者信息，对线上订单进行对账管理，出现异常订单时可以在线退费。
财务管理：对系统产生的交易进行财务管理工作，可以在线对账，导出财务数据，处理异常订单等。
运营数据统计分析：以报表的形式统计分析业务数据，跟踪医生药师的工作量，纳入考核指标。

运营需求分析
短期运营需求分析
提升互联网医院门户平台的流量，实现平台月均访问量明显增长；
提高互联网医院门户平台新用户注册量；
增强用户活跃度，日均活跃用户数（DAU）提升至1万人以上；
扩大品牌影响力，抖音和微信视频号两大平台医院官方账号，关注粉丝量明显增长。
中期运营需求分析
强化重点科室（如中医妇科、内分泌科、眼科、肛肠科等科室）和优势病种（如糖尿病、不孕症、眼干燥症等）的专业形象，建立至少5个具有全国影响力的医生IP或病种IP；
提升线上服务转化率，实现线上问诊订单量明显增长，药品配送服务人次明显提升；
深化用户对品牌的认知，形成以“中医特色诊疗”为核心的差异化竞争优势。
长期运营需求分析
实现线上线下服务闭环，构建完整的医疗服务生态体系，涵盖健康管理、疾病预防、诊疗服务、康复指导等全链条服务；
推动川派中医文化的传承与发展，将成都中医药大学附属医院（四川省中医医院）打造成全国知名的中医药文化品牌；
通过持续优化用户体验和服务质量，成为区域内互联网医疗领域的标杆机构。
用户界面（UI）与使用习惯分析
本项目主要由微信小程序、抖音小程序、PC医生端和PC综合管理后台构成，用户分布为：
微信小程序+抖音小程序：患者
微信小程序+PC医生端：医务人员
PC综合管理后台：信息管理人员以及行政管理人员
用户群体特性分析
患者端
患者群体涵盖不同年龄、文化程度和健康状况的人群，对医疗知识和信息技术的掌握程度差异较大。他们使用系统的核心诉求是便捷获取医疗服务，如在线问诊、预约挂号、查看检查报告等，注重操作的简单易懂和服务的及时性。
医生端
医生作为专业医疗从业者，日常工作节奏快、任务繁重，需要高效处理大量患者信息，进行精准诊断和治疗。他们对系统的功能完整性、数据准确性和操作流畅性要求极高，期望系统能够辅助其提升工作效率和医疗质量。
管理端
管理人员主要负责医院整体运营管理，包括资源调配、数据统计分析、流程优化等工作。他们更关注系统的宏观数据展示、多维度管理功能以及决策支持能力，以便及时掌握医院运营状况，做出科学决策。
用户界面（UI）设计需求分析
患者端UI设计
界面布局：采用极简式布局，突出核心功能入口，如“在线问诊”“预约挂号”“报告查询”等。底部设置固定导航栏，方便用户快速切换常用功能模块。首页可设置搜索框，支持症状、科室、医生等关键词搜索，帮助患者快速找到所需服务。
交互设计：以触摸交互为主，操作步骤尽量简化，避免复杂的层级跳转。例如，在预约挂号流程中，采用分步引导式设计，每一步仅展示必要信息和操作按钮；提供语音导航和操作提示，方便老年患者或视力不佳的用户使用。
视觉设计：采用温暖、柔和的色调，如淡粉色、浅蓝色，营造舒适、亲切的氛围。图标和文字设计注重形象化和易读性，使用大字体和高对比度颜色，确保信息清晰呈现。界面中可适当加入医疗相关的插画元素，缓解患者的紧张情绪。
医生端UI设计
界面布局：采用分栏式布局，左侧为患者信息导航栏，方便医生快速切换查看不同患者的病历、检查报告等资料；中间主区域展示当前患者的详细诊疗信息，包括病史、症状、检验结果等；右侧设置功能操作栏，集成诊断工具、处方开具、检查申请等常用功能按钮。同时，提供自定义布局功能，允许医生根据个人习惯调整界面元素位置和大小。
交互设计：支持键盘快捷键和鼠标手势操作，提高操作效率。在病历书写过程中，提供智能联想输入、模板调用功能，减少重复输入；实现患者信息的实时刷新和同步，确保医生获取最新数据。此外，设计多窗口操作模式，方便医生同时处理多个患者的诊疗任务。
视觉设计：以简洁、专业的风格为主，采用蓝灰色调，体现医疗行业的严谨性。数据表格和图表采用清晰的可视化设计，突出关键信息；文字排版整齐，便于快速阅读和理解。界面元素的颜色和样式可根据功能重要程度进行区分，如紧急提醒采用红色高亮显示。
管理端UI设计
界面布局：采用大屏数据可视化布局，将医院运营关键指标，如门诊量、住院人数、床位使用率等以图表形式集中展示在首页，方便管理人员快速掌握整体情况。左侧设置功能菜单，涵盖人员管理、资源调度、数据分析等模块；右侧区域根据所选功能展示详细数据和操作界面。支持多屏联动和数据抓取功能，方便管理人员深入分析数据。
交互设计：支持鼠标拖拽、缩放等操作，方便用户调整图表大小和布局。提供数据筛选、排序、导出功能，满足不同维度的数据分析需求；在进行资源调配等操作时，设计可视化流程编辑器，通过简单的拖拽和连线即可完成复杂的业务流程配置。此外，设置消息提醒中心，及时推送重要事件和异常情况通知。
视觉设计：采用科技感十足的深色调，搭配高亮数据元素，突出数据的重要性和专业性。图表设计注重美观性和交互性，支持动态数据展示和动画效果；文字采用清晰的字体和合理的间距，确保长时间阅读不易产生视觉疲劳。界面中可加入医院品牌元素，增强品牌认同感。
使用习惯需求分析
患者端使用习惯
碎片化使用：患者通常在闲暇时间使用系统，如排队等候、休息时，使用时间较为碎片化。因此，系统应具备快速加载和恢复功能，确保用户在短时间内完成操作；同时，提供离线缓存功能，方便用户在无网络环境下查看已获取的信息。
社交化分享：患者可能希望将检查报告、问诊建议等信息分享给家人或朋友，获取更多意见。系统需支持一键分享功能，可将信息以图片、链接等形式分享至社交平台；在分享过程中，确保患者隐私信息的安全保护。
医生端使用习惯
高效操作需求：医生日常工作繁忙，要求系统操作便捷、响应迅速。系统应减少不必要的确认步骤和弹窗提示，提供批量处理功能，如批量开具检查单、批量发送复诊提醒等；同时，优化系统性能，确保在高并发情况下仍能保持流畅运行。
数据精准依赖：医生的诊断和治疗决策高度依赖准确的数据支持。系统需保证患者病历、检查报告等数据的及时更新和准确性；提供数据校验和纠错功能，避免因数据错误导致的医疗失误；支持与其他医疗系统的数据对接和整合，实现信息的互联互通。
管理端使用习惯
周期性数据分析：管理人员通常会定期对医院运营数据进行分析，制定决策。系统应提供自定义报表生成功能，支持按日、周、月、季度等时间周期生成各类统计报表；同时，具备数据对比分析功能，方便管理人员直观了解不同时间段的运营变化趋势。
协同管理需求：管理工作往往涉及多个部门和人员的协同合作。系统需支持多人在线协作功能，如共享文档编辑、任务分配与跟踪等；提供即时通讯功能，方便管理人员之间进行沟通和协调；设置权限管理机制，确保不同岗位人员只能访问和操作其权限范围内的数据和功能。
建设方案
建设原则
以患者为中心，以电子病历为核心，以全面集成为手段，以患者满意、医护满意为目标，打造真正全面的现代化智慧医院，作为基础性的智能化和信息化系统要时刻围绕智慧医院的需求和发展方向进行建设。此次项目建设过程中需遵循以下原则：
整体规划，分步实施
医院信息化建设是医院整体建设与发展的一部分，医院信息化建设必须适应医院的整体建设和长远发展。项目建设将遵循集成整合的基本思路，各子系统/模块的软、硬件设计将考虑到符合项目要求的规范进行建设，积极参考国际国内的标准化成果，尽量推动医学信息标准化（如HL7、ICD-10、SNOMED、DICOM3.0等）的使用，并在此基础上架构一个灵活的信息化环境，使得医院信息系统建设在一个标准化、结构化的框架下展开，从而为医院提供一个灵活地面向未来的架构体系。
医院信息系统建设是一项复杂的工程，需要基于不同场景不断切入细化，逐步完成智慧服务互联网一体化平台相关工程的建设，是一个涉及医院方方面面的、复杂而相互关联的子项目集合。为了充分体现数字化医院的综合效益，避免独立孤岛建设，将坚持整体规划，分步实施的原则进行建设。同时，总体规划也将充分切合医院的实际情况，并最大化利用医院现有信息系统的建设成果，保护现有投资。
前瞻性和先进性原则
借鉴国内现代医院管理及信息化建设理念、吸取优秀医院信息化系统建设经验，参照三级医院评审要求、《医院智慧服务分级评估标准体系（试行）》《医院信息互联互通标准化成熟度测评方案》《电子病历功能与应用水平分级评价标准》等标准和要求、结合医院的现有情况，对医院业务及流程进行全面梳理、分析，形成面向未来的整体信息化建设规划，确保信息化系统架构的先进性、前瞻性，适应医院运营特点并能满足医院未来五到十年的信息化发展和管理变革。
实用性和可行性原则
医院信息化建设在考虑前瞻先进的同时，也应考虑到地方和医院的实际情况和实际需求，各子系统的选择和建设应考虑到实用性。在充分利旧的基础上做好顶层设计，避免重复建设和技术冲动，保证建设内容的可行性和必要性。
安全性和可靠性原则
医疗数据交换、业务集成和信息可视化展现所处理、传送和管理的信息，可能涉及不同部门、不同系统、不同患者的秘密或敏感信息，此类信息处理和传递的任何环节如果出现漏洞，其损失将是巨大的；其次，数据交换、业务集成和信息展现承受着大批量的关键性数据和患者隐私数据的流转、交换和存储，系统的安全性和隐私数据脱敏将是系统建设需要重点考虑的问题。为保证数据安全和业务连续性，系统需参照信息安全等级保护三级标准进行建设。
易用性和扩展性原则
医院信息系统功能满足用户的要求是开发工作的出发点和归宿。在成熟的产品上提供客户化定制方法及工具以满足业务特点，相关应用遵循统一的应用架构原则，以确保整体运行效率与成本。信息系统将成为医院管理、运营各方面的支撑环境，因此，系统设计与实施过程将充分考虑系统的稳定性，并具有高可用性，并保证系统支持7×24连续安全运行，稳定可靠，易于维护。
此外，医院自身条件及其外界环境是不断发展变化的。产品从应用到设计不能只满足已知需求的处理能力和性能，应该尽可能不受限制地考虑扩展处理能力，尤其是要考虑与合作业务的关系，延伸业务生命周期。采用开放式体系架构，使系统具备较强的动态适应性。
建设思路
打造线上线下“一体化”+院间协作“同质化”的新模式
以患者为中心，优化线上服务
通过电子健康卡、在线咨询、远程随访等功能，实现患者足不出户即可享受健康管理、复诊咨询等服务，减少线下排队和往返医院的负担。提供预约挂号、线上缴费、病历复印等便民服务，简化就医流程，提升患者体验。
构建智能化医疗协同平台
依托互联网医院，实现远程会诊、影像诊断、双向转诊等功能，促进上下级医院资源共享，提高诊疗效率。通过专科联盟和多中心专病库，推动跨机构协作，提升疑难病症的诊治能力。
强化数据互通与安全共享
利用医疗工作平台和运营监管系统，实现医联体内患者数据互认互享，确保诊疗信息的连续性和安全性。结合物联网技术，优化线上线下服务衔接，如电子处方流转、标本送检等提升医疗资源利用效率。
支持分级治疗与区域医疗协同
通过远程教学、协同问诊等功能，助力基层医疗机构能力提升，推动优质医疗资源下沉。整合医保资源，促进上下级医院分工协作，落实分级诊疗政策，缓解大医院就诊压力。
总体架构
“1个互联网医院门户”：中医特色互联网医院门户
院前服务场景
AI中医体质辨识
构建以人工智能（AI）为基础的中医体质辨识、筛查评估、导诊预问诊工具，帮助用户了解自己的体质类型，进而针对不同体质，基于“未病先防、既病防变、瘥后防复”的理念，根据中医理论进行个性化的健康评估、风险筛查，降低疾病发生风险，促进健康状态维持与恢复，实现从疾病治疗向健康管理的转变。同时可以作为导诊和预问诊的辅助工具，为用户提供就医前的初步咨询和指导，同时有效地吸引和引导具有潜在需求的人群。
治未病业务咨询
治未病业务咨询为患者提供全面的预防保健指导。患者可以在平台上咨询专业的中医医师，了解自身健康状况，获取个性化的养生建议。医师会根据患者的体质、生活习惯等因素，制定适合的预防保健方案，帮助患者预防疾病的发生，提升生活质量。
线上问诊导诊服务
通过互联网医院门户，患者可以随时随地与专业医生进行在线咨询，获取初步诊断建议和就诊指导，极大地方便了患者就医流程，提升了医疗服务的可及性和便捷性。
互联网远程诊疗
利用先进的网络技术和医疗设备，实现医生与患者之间的远程诊断和治疗，打破了地域限制，使优质医疗资源得以广泛共享，尤其为偏远地区的患者提供了高效、专业的医疗服务。
MDT门诊
整合中医和西医的优势资源，采用多学科联合诊疗（MDT）的方式，为患者提供全面、综合的诊疗方案，确保诊断的准确性和治疗的有效性，提升患者的整体治疗效果和生活质量。
精准患者筛查
通过AI技术，对患者的病史、症状、体征等信息进行深度挖掘和精准分析，筛选出高风险或疑似病例，确保患者能够及时得到针对性的诊断和治疗，提高医疗服务的精准度和效率。
院中服务场景
AI智慧就医全流程引导
通过先进的人工智能技术，为患者提供从预约挂号、智能分诊、在线问诊到检查检验、取药缴费等各个环节的全方位、智能化引导，确保患者在就医过程中能够享受到高效、便捷、精准的医疗服务，极大地提升了就医体验。
治未病中心一体化服务
整合了中医养生、健康管理、疾病预防等多方面的资源与功能，构建了一个集健康评估、个性化调理方案制定、跟踪随访于一体的综合性服务平台，旨在帮助人们树立科学的健康观念，实现未病先防、既病防变、瘥后防复的健康管理目标。
智慧住院全流程引导
住院流程全程引导、医嘱实时传递、护理任务智能提醒，全面提升住院服务的智能化水平，确保患者住院期间能够得到更加精细、高效的医疗护理服务，显著提高住院体验和治疗效果。
院后服务场景
专科全病程管理
患者出院后，医生可以通过互联网医院门户帮助患者有效控制病情，提高生活质量。通过全病程管理不仅包括病情监测、用药指导，还涵盖生活方式干预、心理支持等多个方面，旨在为患者提供全面的专病全流程管理方案。
在线复诊续方
为离院患者提供在线复诊开方服务。患者无需亲自前往医院，只需通过在线平台即可与医生进行视频或文字交流，获取专业的病情评估和治疗方案调整建议。在线复诊大大节省了患者的时间和精力，提升了医疗服务的便捷性和可及性。对于确诊患者还可以进行续方，医生可以直接在线开具处方，并通过电子方式发送给患者指定的药店或配送服务，确保患者能够及时获得所需药物，维持治疗效果。这一服务特别适用于慢性病患者或需要长期服药的人群，减少了他们频繁往返医院的麻烦，提高了就医效率和患者满意度。
中医延续护理
患者离院后，可以持续提供中医护理上门护理，包括针灸、推拿、拔罐、刮痧等中医特色疗法，以满足不同患者的个性化护理需求。护理团队会根据患者的具体情况，制定详细的护理计划，并通过定期随访和评估，确保护理效果。同时，中医延续护理还注重患者家属的培训，教会他们一些基本的中医护理技能，以便在家庭环境中也能为患者提供有效的护理支持。
云药房（患者）
患者可以根据医生在线处方或者自主开方，通过互联网医院门户随时随地购买药品，享受便捷的送药上门服务。云药房不仅提供丰富的药品选择，还具备智能药品推荐、用药提醒等功能，确保用户用药安全、便捷。
健康商城（治未病）
为广大用户提供便捷、高效、全面的健康商品和服务。特别是治未病用户可以通过互联网医院门户轻松购买到各类健康产品，如保健品、医疗器械、健康食品等，同时还能享受到专业的健康咨询和个性化推荐服务。
院后随访
建立紧密的随访机制，确保患者在出院后仍能得到持续的关注和专业的指导。通过定期的电话随访、短信提醒以及在线问卷等方式，收集患者的健康状况信息，监测病情变化，及时发现并解决潜在的健康问题。随访过程中，医护人员会根据患者的实际情况，提供个性化的康复建议和生活方式调整指导，帮助患者更好地恢复健康，预防旧病复发。此外，院后随访还包括对患者家属的教育和指导，确保家属能够正确理解和执行患者的护理计划，共同促进患者的康复进程。这一紧密的随访机制，不仅提升了医疗服务的延续性和完整性，也增强了患者对医院的信任和满意度。
“运营服务”：运营服务体系
互联网医院系统建设与运营一体化
互联网医院系统建设与运营是一体的。从系统建设角度看，互联网医院搭建起了数字化医疗服务的基础框架，涵盖在线问诊、电子病历管理、远程医疗等多元功能模块，实现医疗流程的线上化与数据化。但倘若缺乏与之匹配的运营，这些功能便如同空壳，无法充分发挥价值。运营一体化能让建设成果落地生根，通过有效的推广策略，提升患者对互联网医院服务的知晓度与使用率；从患者体验层面，一体化能打造无缝对接的就医流程，患者借助建设成果在线预约挂号、查询检验报告，运营端则跟进服务质量，及时处理患者反馈，优化操作界面，使患者使用更便捷；对医院而言，建设构建了医疗资源整合与分配的数字化平台，运营则从绩效激励、业务拓展等方向，推动医生积极参与线上诊疗，拓展业务范围，增加医院影响力与收益。互联网医院系统建设与运营一体化，是提升医疗服务效率、优化患者体验、促进医院可持续发展的核心驱动力，二者相辅相成，缺一不可。
门户专区活动
IP打造
构建医生个人品牌IP、重点优势病种的专业IP以及医院整体品牌IP，同时精心打造具有独特魅力的特色中医IP，通过这一系列多维度的品牌建设策略，形成多位一体的综合性品牌矩阵，旨在全面提升医疗服务的品牌影响力和市场竞争力。
线上引流
通过精心策划和实施，利用抖音和微信视频号这两个强大的社交媒体平台，实现精准的市场布局。抖音以其庞大的用户基础和高度活跃的社区环境，为内容创作者和商家提供了一个展示和推广产品的绝佳场所。微信视频号则依托于微信这一国民级应用，拥有庞大的用户流量和高度成熟的社交网络，为内容传播和用户互动提供了便利。
线下活动策划
线下活动是互联网医院门户平台实现线上线下联动的重要抓手，通过举办主题义诊、健康讲座和名医面对面等活动，不仅可以增强用户对平台的信任感，还能有效引导线下用户向线上转化。‌
业务架构

患者就医业务流程（线上线下一体化）
互联网医院
线上问诊/分诊：患者首先需要登录到中医特色互联网医院的统一门户网站，在该平台上，患者可以通过AI智能客服系统进行线上问诊或分诊服务。AI智能客服将根据患者的症状描述和初步信息，进行初步的诊断和分诊建议，帮助患者快速找到合适的科室和医生，从而提高问诊效率。
视频问诊与中医辨证：如果AI智能客服判断患者需要进一步的门诊治疗，患者可以在平台上进行线上挂号，选择合适的医生和时间。挂号成功后，患者将通过视频通话的方式与医生进行详细的问诊，医生会根据患者的具体症状和体质，运用中医辨证的方法进行深入分析和诊断，制定个性化的治疗方案。
在线支付与服务：患者在完成在线支付相关费用后，即可享受到多种在线服务，包括但不限于在线咨询、就医引导等。若患者需要进行复诊，可以通过平台进行在线复诊预约，并获取医生的报告解读服务。此外，医生还可以在线为患者续方，并提供用药提醒，确保患者能够按时按量服药，提高治疗效果。
住院引导（如需）：对于病情较为严重、需要住院治疗的患者，互联网医院将提供全面的住院引导服务。这包括向患者详细告知住院所需的清单和准备事项，安排日间手术的具体时间，以及出院时提供带药指导和出院小结等服务，确保患者在住院期间能够得到全方位的关怀和指导，顺利完成治疗过程。
线下门诊
线下挂号流程：患者亲自前往医院，在挂号窗口或自助挂号设备上进行线下挂号操作。具体步骤包括排队等候、提供个人信息、选择科室和医生、支付挂号费用，最终获取挂号凭证。
到院初诊环节：患者携带挂号凭证前往相应科室，等待叫号后进入诊室。医生对患者进行详细的四诊分析，包括观察面色、舌苔（望诊），闻听声音、气味（闻诊），询问病史、症状（问诊），以及进行脉象检查（切诊），以全面了解患者病情。
诊疗服务内容：医生根据四诊分析结果，综合判断后开具中药处方。患者持处方到中药房抓药，药师按方配药。若需进一步明确诊断，医生会安排相应的检验检查项目，如血液检测、影像学检查等。对于病情稳定但需持续治疗的患者，医生会详细告知中医医嘱，并安排后续复诊时间。而对于病情不稳定或需密切观察的患者，医生则会安排住院治疗，以便进行更全面的医疗监护和干预。
住院服务
住院登记及前期评估：患者到达医院后，首先需要进行住院登记手续，这一过程包括填写相关表格、提供个人信息和病史资料等。随后，医护人员将为其开展全面的四诊建档工作，即通过望、闻、问、切等中医传统诊断方法，详细记录患者的身体状况。此外，还将进行一系列必要的检验检查，如血液检测、影像学检查等，以确保对病情的全面了解。同时，护理团队会对患者进行细致的护理评估，评估内容包括患者的日常生活能力、心理状态以及潜在的健康风险，为后续的治疗和护理提供科学依据。
住院治疗：根据患者的具体病情和诊断结果，医疗团队将制定个性化的治疗方案。这可能包括安排手术治疗、中药内服或外用治疗等多种手段。在治疗过程中，医院不仅提供专业的中医护理服务，如针灸、推拿等，还注重患者的健康教育，通过开展健康讲座、发放宣传资料等方式，帮助患者及其家属了解疾病相关知识，掌握自我护理的方法，从而提高治疗效果和生活质量。
出院后护理：患者出院后，依然可以享受到医院提供的延续性护理服务。通过中医护理平台，患者可以获得包括中医特色护理、临床基础护理以及专科护理在内的全方位护理支持。此外，针对行动不便或需要特殊照顾的患者，医院还提供上门护理服务，患者或家属可以通过预约申请，享受专业护理人员上门提供的各项护理措施，确保患者在家庭环境中也能得到高质量的医疗护理，促进康复进程。
亚健康人群治未病业务流程
互联网医院
体检预约与智能辨识：通过互联网医院的统一门户平台，在AI健康管家的全方位协助下，用户可以便捷地进行体检预约服务。同时，系统会利用先进的中医智能体辨识技术，对用户的身体状况进行全面而精准地分析和评估。
风险分层：根据中医智能体辨识所得到的详细结果，系统会科学地进行风险分层管理，具体分为高风险（红色预警）、中风险（黄色警示）和低风险（绿色安全）三个等级，以便后续进行针对性的健康管理。
个性化方案制定：依据风险分层的具体等级，系统会为每位用户量身定制个性化的健康方案。该方案不仅涵盖全面的健康管理措施，还包括中医调理和非药物疗法，如睡眠管理、情绪管理、饮食运动指导、中药干预、针灸艾灸治疗以及季节性贴敷等多种手段。
健康管理服务：借助中医AI专病全病程管理系统，用户可以享受到丰富的健康管理服务，包括健康宣教视频的精准推送、健康管家专病管护计划的制定与执行、医护人员的定期随访服务。此外，通过智能监测设备对用户的健康状况进行动态跟踪与预警，一旦发现异常情况，系统会及时反馈并灵活调整健康管理方案。
治未病中心
预约挂号服务：针对亚健康状态的人群，可以前往治未病中心进行实体挂号服务或者线上预约挂号。通过现场挂号，能够更直接地与医疗人员进行面对面交流，确保挂号过程的顺利进行，同时也为后续的诊疗服务奠定基础。
中医评估与体质分类流程：在治未病中心，专业中医师会对前来就诊的患者进行全面的中医评估。这一评估过程包括详细的问诊、望闻问切等传统中医诊断方法，最终依据中医九大体质分类理论，精准确定每位患者的个人体质类型，为后续的个性化治疗提供科学依据。
个性化健康方案定制服务：根据不同人群的具体健康需求，治未病中心采用中西医结合的方式，结合中医体质分类和现代监测设备采集的数据，制定出针对性的健康管理方案。
针对不同人群的不同干预手段：针对健康管理人群，提供睡眠管理和情绪管理服务；针对中医调理人群，进行适时运动管理和中药干预；针对非药物疗法人群，采用针灸、艾灸以及季节性敷贴等传统中医治疗方法。
医生评估与研判措施：在管理一段时间后，医生会对未病人群的健康情况进行重新评估，并根据评估结果对健康管理方案进行必要的调整。对于一类特定人群，医生会进行家庭健康干预，提供长期的健康促进服务。这类人群还可以通过线上商城购买中药养生产品和中药茶饮，享受送货到家的便捷服务，或者通过互联网护理平台获得长期的健康护理支持。对于健康风险升级的人群，医生会及时进行就医引导，并提供多学科MDT（多学科综合治疗）预防措施，确保患者健康得到全方位的保障。
门诊患者转治未病
转治未病中心
对于康复的患者，可以通过平台引流到治未病中心，为患者提供中医特色的康复与健康管理服务。
AI健康评估
充分利用AI技术的优势，对患者进行个性化的健康评估，并根据评估结果，为其制定一套涵盖饮食调养、运动锻炼、心理调适等多方面的康复计划。通过这一系列的服务，帮助患者在康复期间更好地恢复身体健康，同时提升他们的生活质量。
治未病健康管理社区
为了进一步增强患者的参与度和满意度，还可以打造一个互动式的健康管理社区。在这个社区中，患者可以分享自己的康复经验，交流健康养生知识，甚至参与线上或线下的健康活动。这样的互动不仅能增强患者的健康意识，还能让他们感受到来自社区的温暖和支持，从而更加积极地面对康复过程。
门诊用户反馈
还可以建立一个完善的反馈机制，及时收集患者对于各项服务的意见和建议。通过这样的方式，能够不断优化服务流程，提升服务质量，确保每一位患者都能得到最贴心、最有效的健康管理服务。
治未病转门诊就医
转门诊预约
对于治未病中心发现的重点关注人群和存在潜在健康风险或需要进一步检查治疗的人群，可以提供一条顺畅的转介路径，通过预约挂号、就医引导使其能够无缝对接到线下门诊进行更深入的诊断和治疗。这一转介过程将确保信息的连续性和准确性，患者在治未病中心所做的所有评估和检测结果都将直接传输到对应门诊科室的医生工作站，以便医生能够全面了解患者的健康状况，制定更为精准的治疗方案。
门诊就医服务
门诊医生将根据患者的具体情况，结合治未病中心的评估结果，制定个性化的治疗方案。治疗方案可能包括药物治疗、物理治疗、中医治疗等多种手段，旨在全面改善患者的健康状况，降低疾病发生的风险。
随访服务
对转介到门诊的患者进行定期跟踪和评估，能够及时了解患者的治疗效果和健康状况变化，为后续的治疗方案调整提供科学依据。同时，也将为患者提供健康宣教、生活方式指导等全方位的健康管理服务，帮助他们更好地管理自己的健康，提升生活质量。
安全架构
安全建设目标
项目建设内容按等保3.0 三级标准建设，遵循等级保护的相关标准和规范的要求，结合信息系统安全建设实际状态。针对信息系统中存在的安全隐患进行系统建设，加强信息系统的信息安全保护能力，使其达到等保三级保护安全要求。
安全建设原则
1．遵循《信息安全技术 网络安全等级保护基本要求》（GB-T 22239-2019 ），《信息安全技术 网络安全等级保护安全设计技术要求》（GB/T 25070-2019）等相关标准和规范的要求；
2．按照本技术要求进行设计，保证系统结构完整，安全要素全面覆盖；
3．统一规划、分步实施。网络与信息安全体系建设是一个逐步完善的过程，各单位应依据本技术要求进行统一规划，在建设时可以根据信息化的发展逐步建设与完善，首先保证重要信息系统的安全；
4．在保证关键技术实现的前提下，尽可能采用成熟产品，保证系统的可用性、工程实施的简便快捷。
数据存储安全
分布式存储：互联网医院的数据量庞大，包括患者的病历、检查检验报告、健康监测数据等。采用分布式存储系统，如分布式文件系统（Ceph、GlusterFS 等），可以将数据分散存储在多个服务器节点上。这样做的好处是提高了数据的可用性和可靠性，即使某个存储节点出现故障，数据也不会丢失，其他节点仍能提供数据访问服务。
加密存储：对于患者的敏感数据，如身份信息、疾病诊断结果、电子处方等，采用加密算法（如 AES、RSA 等）进行加密存储。在存储过程中，数据以密文形式存在，只有通过授权的解密密钥才能将其还原为明文。例如，患者的身份证号码在存入数据库之前，会经过加密处理，即使数据库被非法访问，窃取者也无法直接获取到真实的身份证号码信息。
数据备份与恢复策略
定期备份：制定严格的数据备份计划，按照一定的时间间隔（如每日、每周）对数据进行全量或增量备份。备份数据存储在异地的数据中心，以防止本地数据中心因自然灾害、火灾等不可抗力因素导致数据完全丢失。例如，每天凌晨对前一天的新增患者病历和更新的检查检验报告进行备份，并将备份数据传输到位于另一个城市的备份服务器上。
恢复测试：定期进行数据恢复测试，确保在数据丢失或损坏的情况下能够快速、准确地恢复数据。通过模拟各种数据丢失场景，如存储设备故障、数据误删除等，检验备份数据的完整性和恢复流程的有效性。
数据传输安全
网络协议与加密
使用安全协议：在数据传输过程中，采用安全的网络协议，如 HTTPS（Hypertext Transfer Protocol Secure）来保护数据的保密性和完整性。HTTPS 通过 SSL/TLS（Secure Sockets Layer/Transport Layer Security）加密协议对数据进行加密传输，确保患者和医生之间的通信内容（如在线问诊的文字消息、检查检验数据传输等）不被窃取或篡改。
数据加密技术：对于一些特别敏感的数据，如电子处方中的药品信息、患者的基因检测数据等，除了网络协议加密外，还可以采用端到端加密技术。即数据在发送端进行加密，只有在接收端使用特定的密钥才能解密，中间传输过程中的任何节点（如网络服务器、代理服务器等）都无法获取明文数据。
数据完整性验证
消息认证码（MAC）：为了防止数据在传输过程中被篡改，使用消息认证码技术。发送方在发送数据时，会根据数据内容和密钥生成一个 MAC 值，并将其与数据一起发送给接收方。接收方收到数据后，使用相同的算法和密钥重新计算 MAC 值，然后与接收到的 MAC 值进行比较。如果两者一致，则说明数据在传输过程中未被篡改；否则，数据可能已经被修改，接收方会拒绝接收该数据。
数据访问安全
身份认证与授权
用户身份认证：对于患者、医生和管理人员等不同角色的用户，采用多种身份认证方式。患者通常可以使用手机号、身份证号码、密码以及生物识别技术（如指纹识别、面部识别）进行身份认证。医生和管理人员除了基本的账号密码认证外，还需要提供执业资格证书编号等专业资质信息进行认证。例如，医生在登录互联网医院平台时，除了输入账号密码外，还需要通过验证其医师资格证书编号来确保其合法执业身份。
访问授权管理：根据用户的角色和职责，为其分配不同的访问权限。患者只能访问自己的病历、检查检验报告和健康管理相关信息；医生可以访问自己负责患者的全部医疗信息，包括病历、诊断、治疗方案等，并具有一定的医疗操作权限（如开具电子处方、申请检查检验等）；管理人员则可以访问医院的运营数据、医生和患者的综合信息，用于管理和监控医院的整体业务。通过访问控制列表（ACL）、基于角色的访问控制（RBAC）等技术实现细粒度的访问授权管理。
数据审计与监控
审计日志记录：系统会记录所有的数据访问操作，包括访问的用户、访问的时间、访问的数据内容、操作类型（如查询、修改、删除）等信息，形成审计日志。这些日志可以帮助管理人员追踪数据的访问情况，及时发现异常访问行为。例如，如果发现某个用户在非工作时间频繁访问大量患者的敏感信息，就可以通过审计日志进行调查，判断是否存在数据安全风险。
实时监控与预警：通过数据访问监控系统，实时监测数据访问行为。当发现异常访问模式（如大量数据下载、频繁尝试访问未授权的数据）时，系统会自动发出预警信号，通知安全管理人员进行处理。可以采用机器学习和行为分析算法来建立数据访问行为模型，提高异常检测的准确性和效率。
网络访问安全
防火墙与入侵检测系统（IDS）/入侵防御系统（IPS）
防火墙设置：在互联网医院的网络边界部署防火墙，根据预先定义的规则，允许或禁止网络流量的进出。防火墙可以根据源 IP 地址、目的 IP 地址、端口号、协议类型等因素进行访问控制。例如，只允许来自合法医疗机构 IP 地址范围的访问请求访问医院内部的医疗信息系统，阻止外部非法 IP 地址对内部网络的扫描和攻击；
IDS/IPS 应用：IDS 可以监测网络中的入侵行为，如恶意扫描、SQL 注入攻击、DDoS（分布式拒绝服务）攻击等。当检测到入侵行为时，IDS 会发出警报。IPS 则不仅能够检测入侵行为，还能够采取主动防御措施，如阻断攻击流量、修改防火墙规则等。通过 IDS/IPS 的结合使用，可以有效地保护互联网医院网络免受外部攻击。
虚拟专用网络（VPN）与安全访问通道
VPN 技术应用：对于需要远程访问互联网医院内部系统的医生和管理人员，如在家办公或外出会诊的情况，使用 VPN 技术建立安全的远程访问通道。VPN 通过加密和隧道技术，将用户的网络流量封装在安全的通道中传输，确保数据在公共网络（如互联网）中的保密性和完整性。例如，医生在家中通过 VPN 登录互联网医院的医疗信息系统，就像在医院内部网络一样安全地访问患者的病历和进行诊疗操作；
安全访问通道配置：根据不同的用户角色和访问需求，配置不同的安全访问通道。例如，医生访问患者医疗数据的通道和管理人员访问医院运营数据的通道可以设置不同的加密算法、访问权限和安全级别，以满足多样化的业务需求。
应用访问安全
应用层身份验证与授权
多因素认证应用：在应用层面，除了传统的账号密码认证外，采用多因素认证方法来增强访问安全性。例如，在用户登录互联网医院平台时，除了输入用户名和密码外，还需要输入通过手机短信验证码或硬件令牌生成的一次性密码。这样即使密码被泄露，攻击者没有其他认证因素也无法成功登录；
授权管理细化：对应用的功能模块和数据资源进行细粒度的授权管理。例如，在患者端应用中，患者可以查看自己的病历和健康管理建议，但不能修改诊断结果；在医生端应用中，不同科室的医生只能访问和操作自己科室相关的患者信息和诊疗功能，如内科医生不能修改外科患者的手术记录。
安全漏洞扫描与修复
定期漏洞扫描：定期对互联网医院应用程序进行安全漏洞扫描，使用专业的漏洞扫描工具（如 Nessus、Acunetix 等）检测应用程序代码中的安全漏洞，如 SQL 注入漏洞、跨站脚本攻击（XSS）漏洞、文件包含漏洞等。扫描范围包括 Web 应用、移动应用以及后台服务等各个组件；
及时修复漏洞：一旦发现安全漏洞，开发团队需要及时进行修复。根据漏洞的严重程度，制定相应的修复计划，优先处理高风险漏洞。同时，在修复漏洞后，需要进行回归测试，确保修复措施不会引入新的问题，并且应用程序的功能和性能不受影响。
网络架构
本地中心与云中心之间网络架构


本项目采用本地化+云部署，为确保数据安全性，数据传输采用VPN加密隧道或云专线的模式。
IPsec VPN连接分为VPN网关、用户网关和IPsec连接三部分，通过配置相关加密协议，在VPN网关与用户网关之间建立IPsec VPN隧道，实现云上VPC与本地数据中心、其他云上VPC之间的安全连接。IPsec连接绑定VPN网关，可实现本地数据中心与云上VPC之间的网络互通。
通过专线、L3VPN（三层虚拟专用网）提供区别于公网的独立通道，实现端到端的通信，实现企业数据中心混合云架构、异地容灾和多区域业务互联等应用场景
本地中心本地化网络架构

其中本地化部署内外网环境通过网闸进行隔离，经过多重的隔离和防护，保证院外请求访问院内数据的链路安全。系统拓扑采用当下最流行的互联网安全架构，兼顾安全、开放、灵活的架构原则：
采用严格的内外网分级隔离设计；
统一鉴权，网络通讯加密处理；
开放的API网关支持机构间互联和多系统轻量化集成管理。
推理服务器分成P节点和D节点两组，根据模型种类和业务负载，确定PD比例，实际部署以测试寻优比例为准。
PD分离架构需要独立部署MindIE MS coordinator和Controller节点。
业务面交换机按照业务需求确定选型，典型配置25GE或100GE交换机。
参数面采用全互联交换，优先使用一层交换，根据超节点规模选取Leaf和Spine交换机数量。

技术架构
技术架构采用互联网领域最领先的技术堆栈，采用广泛应用的成熟开源软件中间件，保障性能、安全的前提下，提供未来的可扩展能力和可维护性。
同时用户体验参照行业最先进的UI交互模式，从APP、WEB、PC等各种设备做到最佳设计。
安全的角度也考虑了加密通讯、存储，移动设备管理，水印，数字签名，日志留痕（用户体验管理）等。
所有的应用服务器和存储都考虑了高可靠性，提供容错能力，防止服务宕机，数据丢失等，整体达到99.9%的标准IT系统可靠性。

标准化质控体系
接口级测试驱动，全流程测试跟踪。将全面质量管理的思想运用于软件研发体系，实现从需求管理到项目计划、项目控制、软件获取、质量保证、配置管理的软件过程全面质量管理。从组织层面上实施过程质量管理，强化对软件开发的过程进程控制，助力提高产品质量，降低生产成本。
标准化实施交付
平台采用热插拔功能组件，核心功能基于参数配置可实现业务动态调整，可快速交付上线，支持本地部署和云端部署。
标准化安全运维
平台基于企业级的JAVAEE技术栈，采用JVM虚拟机级安全监控，技术团队开发环境联动内部管理工具钉钉，风险故障等运行环境不利因素自动化监测判定，并及时通过顶顶机器人推送风险告警信息。
数据安全技术体系
构建全生命周期数据安全技术体系，从数据采集、数据存储、数据传输、数据交换、数据共享、数据分析、数据使用全流程对数据进行合规化使用与治理。
通过数据安全治理评估、数据安全组织结构建设、数据安全管理制度建设、数据安全技术保护体系建设、数据安全运营管控建设、数据安全监管建设这六方面构建起数据安全保障体系。
数据+业务双中台架构
采用多域、多模态、多源异构数据采集总线，构建HIS/LIS/EMR数据采集器、区域平台数据采集器、蓝牙设备数据采集器、多源异构数据采集器，再通过ETL工具对数据进行采集、转换、加载，经过多维模型处理后建立数据仓库，用于统一数据管理、数据多维分析等。
在数据中台之上构建业务中台，形成用户中心和业务中心，将患者、医生、护士、机构管理人员等角色统一纳入用户中心里面进行授权/鉴权，在业务中心里面构建预约服务、支付服务、健康档案服务、上门过程服务等单体微服务，前台以搭积木的形式将各种服务串联即可开展业务运行。
人工智能技术
本项目将引入人工智能，如deepseek、火山大模型等，并将其抽象成AI智能体为患者提供服务，AI智能体可以对患者的提问进行语义理解，识别患者描述的症状是属于哪个身体系统（如消化系统、呼吸系统等）的问题，并给出初步的引导性回答。在医患对话过程中，可以帮助医生快速提取患者关键信息，如症状出现的时间、频率等，提高问诊效率。详见全场景患者服务AI智能体建设部分。
部署架构
为保障成都中医药大学附属医院（四川省中医医院）互联网医院平台在高并发访问、多业务协同、数据安全合规等方面具备良好的支撑能力，系统采用混合云部署模式 ，结合本地私有云与公有云资源，构建一个弹性扩展、安全可控、高效稳定 的部署架构体系。该架构支持平台核心服务的持续运行、快速响应及灵活扩容，满足医院当前业务需求与未来发展的长期演进。
整体部署架构图

为了满足患者日益增长的医疗需求，创新服务模式，提高服务效率，降低服务成本，医疗机构从开展预约挂号、在线支付、检验检查查询、诊后随访等互联网+应用，逐步发展到开展互联网医院。
互联网医院是应用互联网等信息技术拓展医疗服务空间和内容，构建覆盖诊前、诊中、诊后的线上线下一体化医疗服务模式。医疗机构可以在实体医院基础上，使用互联网医院作为第二名称，运用互联网技术提供安全适宜的医疗服务，允许在线开展部分常见病、慢性病复诊，拓展了医疗服务半径。医师掌握患者病历资料后，允许在线开具部分常见病、慢性病处方，提高了医疗服务效率。患者到医院就医不用到窗口排队，通过手机可以实现网上挂号、预约诊疗、就诊提醒、移动支付、检查检验结果在线查询等，部分常见病、慢性病复诊可以直接通过互联网医院完成，极大减少就医时间方便患者，提高了医疗服务的可及性。
这些业务场景面向互联网，关注患者使用体验、医疗数据安全隐私，这就对传统IT部署带来了挑战：
互联网安全难以保证：
2023年8月，某知名医科大学附属医院发现境外某IP地址对医院服务器进行了3000余次的非法访问，成功获取敏感数据2100余条，经分析，该IP的活动特征属于典型的网络爬虫，而医院缺乏相应防范措施。2023年，国内某三甲医院由于接口未进行鉴权设定，导致被某国内银行IP连续10余天，通过2个API接口全天候访问数据，返回数据结果约4万条，涉及个人信息、病例信息、医生信息等，造成极大隐患。另外，根据美国非营利性组织身份盗用资源中心(ITRC)的统计数据显示，医疗健康信息系统的漏洞数占全行业的43.8%。分析原因首先是医疗机构在安全能力和意识上刚刚起步，很少有专职的安全工程师，很多医院对安全的理解还认为靠边界防护和网络隔离就能保障安全，产生内网很安全的错觉。在复杂的攻击形势下，60%的医疗行业的网络安全事故，都是因为同一个误区：认为隔离就是安全。
业务高峰期往往难以应对：
互联网应用面向人群众多，挂号、就诊存在业务高峰期，例如某时段的问诊量是其他时段的几倍，导致应用响应延迟，最终只能按照高峰期要求扩容，非高峰期时段造成了资源的浪费。
如何提升用户体验：
在移动互联网时代，自诊自查、医生问诊、预约挂号、医生学术分享交流都需要良好的网络环境作为保障。原有机房缺乏多线接入能力，网络效果难以满足用户体验要求。
 HYPERLINK "http://static-aliyun-doc.oss-cn-hangzhou.aliyuncs.com/assets/img/85620/154691643735894_zh-CN.png" 
网络部署安全隔离
在阿里云中划分医院的专有网络（VPC），使用隧道技术达到与传统 VLAN 相同的隔离效果，并通过专线/VPN/智能接入网关的连接方式将 VPC 与医院数据中心组成一个按需定制的网络环境，包括选择自有 IP 地址范围、划分网段、配置路由表和网关等。VPC内部各个集群之间使用安全组功能，划分成不同的安全域，定义不同的访问控制规则，保证互联网区域和非互联网区域的安全可控。
通过专线、VPN网关、智能接入网关将本地数据中心和云上VPC打通，三种连接方案说明：
采用专线的方式：通过租用一条运营商的专线将本地数据中心连接到阿里云接入点，建立专线连接，物理专线接入网络质量好，带宽高但成本稍高，如果对网络质量有很高的要求，建议选择高速通道专线接入。
采用VPN的方式：通过VPN网关的IPsec-VPN将本地数据中心和VPC连接起来，基于Internet通信，网络延迟和可用性取决于Internet，如果对网络延迟没有特别高的需求，建议选择VPN网关，快速接入成本更低
采用智能接入网关：通过智能接入网关设备，可直接复用已有的Internet固定宽带接入方便快捷，偏远地区或移动办公使用4G网卡接入，接入不受环境限制。双链路密封接入，自动探测最优链路，故障时主动实时切换，实现Internet就近加密接入，获得更加智能、更加可靠、更加安全的上云体验。智能接入网关配置简单，成本低。
以上可以组合使用各种接入方案专线+VPN、专线+智能接入网关，通过双线路容灾构建高可用混合云。
服务保证高可用
用户的请求被负载均衡分发到应用服务器集群进行处理，负载均衡（SLB）能消除单点故障，保证服务的高可用。
随着业务的增长，云服务器（ECS）支持升级CPU和内存，在线不停机升级带宽，可以快速升级资源配置，满足业务增长的要求。
在业务高峰期，可以采用弹性伸缩服务（ESS），自动对集群进行横向扩展和回缩，实现服务器的快速部署与扩容。
业务数据写入云数据库（RDS）中，云数据库采用一主一备的双机热备架构，自动同步数据，主机出现故障后备机秒级完成无缝切换。
安全服务保证数据隐私
通过云安全防护体系：
强化网络访问控制，划分安全区域加强对网络的访问控制，安全隔离措施相当于“安全门”，阻止恶意木马快速蔓延。
定期安全测试，发现安全漏洞，通过网站漏洞扫描和渗透测试服务，减少可能被利用入侵的突破口。
建立全局的外部威胁和情报感知能力，通过服务器安全、Web应用防火墙、SSL证书服务、数据库审计、堡垒机对医疗机构的IT系统做好基础防护，通过态势感知能够及早发现潜在的威胁，保障医疗数据隐私安全。
建立应急响应流程和预案，通过安全管家服务持续化的安全运营。
运维监控效率提升
云监控服务用于收集获取阿里云资源的监控指标或用户自定义的监控指标，探测服务可用性，以及针对指标设置警报。运维人员可以全面了解资源使用情况、业务的运行状况和健康度，并及时收到异常报警做出反应，保证应用程序顺畅运行。
各类通知快速触达
互联网医院移动/网站注册、安全登录、支付认证、身份认证、密码找回、账号绑定等可以直接通过短信服务，国内短信三网合一专属通道快速接入，支持变量内容灵活，秒级可达99%到达率。
部署环境

为保障成都中医药大学附属医院（四川省中医医院）互联网医院平台在高并发访问、多业务协同、数据安全合规等方面具备良好的支撑能力，系统建议采用混合云部署模式，构建一个弹性扩展、安全可控、高效稳定的部署架构体系。该架构支持平台核心服务的持续运行、快速响应及灵活扩容，满足医院当前业务需求与未来发展的长期演进。
互联网接入区
网络接入保障：
支持中国移动、中国联通、中国电信三大运营商线路100M及以上带宽接入，具体带宽根据实际业务需求动态调整。
支持同运营商双链路自动切换与负载均衡，提升网络稳定性与访问效率。
支持SSL证书硬件卸载，实现HTTPS加密流量的高性能处理，降低服务器压力。
安全防护机制：
部署防火墙、入侵防御系统（IPS）、Web应用防火墙（WAF）等设备，实时过滤异常流量，防止DDoS攻击、SQL注入、跨站脚本等常见网络安全威胁。
配置入侵监测系统（IDS），对网络行为进行非实时分析，及时发现潜在风险并预警。
容灾切换能力：
通过全局负载均衡（GSLB）技术，实现同城异地数据中心之间的自动容灾切换，保障平台在单点故障情况下的持续服务能力。
非军事化区
网络冗余与保护：
所有对外提供服务的下行链路均实行双路冗余配置，支持链路健康检查与自动切换，确保服务连续性。 对接外部系统的服务器部署于DMZ区域，并实施必要的网段隔离策略，防止横向渗透攻击。
访问控制与隔离：
DMZ区内服务器间通过软件防火墙或虚拟防火墙进行访问控制，限制不必要的通信路径。
所有进出DMZ区域的流量均需经过两层严格防火墙策略控制，确保仅开放必要端口和服务。
业务服务区
服务负载均衡：
业务中间层服务器采用硬件负载均衡设备（如F5）或软件负载均衡（如Nginx+Keepalived），实现请求分发、会话保持、故障转移等功能，提升系统整体性能与可用性。
网络隔离与访问控制：
不同业务模块之间实施严格的网络隔离策略，避免不同功能组件间的直接通信干扰。
基于RBAC模型配置访问控制策略，确保各系统模块之间仅开放最小权限接口。
数据存储区
高性能存储架构：
存储系统采用双控制器存储阵列，支持RAID 10/6等冗余策略，构建专用存储网络（SAN/NAS），实现IO性能>10万IOPS，满足高并发读写需求。
数据库高可用设计：
构建基于主从复制、集群部署的数据库高可用架构（如MySQL Cluster、Oracle RAC、MongoDB Replica Set），确保数据库服务在节点故障时仍能正常运行。
影像文件分布式存储：
对占用空间大、碎片化严重的影像类文件（如舌象、脉象采集图像、视频问诊记录等），采用分布式文件系统（如Ceph、HDFS）进行集中存储与管理，支持水平扩展与自动容错。
外联对接区
多专线接入与链路保护：
支持医保系统、省级健康信息平台、医联体成员单位等多条专线接入，确保关键业务通道稳定可靠。
每条专线均配备链路冗余与故障切换机制，保障外联服务不间断运行。
安全隔离与访问控制：
外联系统与内部核心系统之间设置独立防火墙，严格限制访问范围和操作权限，防止越权访问与数据泄露。
灾备系统
同城双中心容灾架构：
建设“同城双活”容灾中心，核心业务系统实现双中心并行运行，具备自动切换能力，保障平台在突发灾难场景下的业务连续性。
数据备份与恢复机制：
关键数据实现本地实时备份，重要业务系统日志与状态信息每日多次同步至异地灾备中心，支持近实时恢复。
定期开展灾备演练，验证灾备系统的有效性与恢复时效。
运维管理区
远程运维支持：
支持运维人员通过安全通道（如堡垒机、SSH跳板）对系统资源进行远程访问与操控，提升运维效率。
权限管理与审计追踪：
系统内置完善的运维审计机制，记录所有运维操作日志，便于事后追溯与责任定位。
运维账号按角色分级授权，确保权限最小化原则。
性能监控与告警：
部署统一的运维监控平台（如Prometheus+Grafana、Zabbix），实现对计算、存储、网络资源的实时性能监控与异常告警。
资源热分配与调度：
支持对计算、存储、网络资源的动态热分配，可根据业务负载变化自动调整资源配置，提升资源利用率与系统响应速度。
系统接口设计
为实现成都中医药大学附属医院（四川省中医医院）互联网医院平台与现有院内系统的无缝集成，构建线上线下一体化、AI赋能、全病程管理的智慧医疗服务体系，系统需与多个核心业务系统进行深度对接。通过标准化接口（RESTful API、WebService、HL7/FHIR等）实现数据共享、流程联动和业务协同，保障平台功能的完整性与稳定性。
以下列出了平台建设过程中需要对接的主要系统及其接口内容：
医院信息系统
接口名称
功能描述
患者信息同步接口
获取患者基本信息（姓名、身份证号、手机号、性别、年龄、医保类型等），用于平台注册认证及就诊记录关联
挂号排班接口
获取医院科室、医生排班信息，支持线上预约挂号、分时段预约
就诊卡绑定接口
实现平台用户与院内就诊卡/住院号的绑定，便于后续诊疗信息调用
收费项目接口
获取挂号费、检查费、药品费等收费标准，用于在线支付结算
医保结算接口
联动医保系统，实现线上挂号、复诊、药品费用的医保实时结算
支付对账接口
对接医院财务系统，完成线上支付订单与医院账务数据的核对与清算
电子病历系统
接口名称
功能描述
门诊病历查询接口
获取患者门诊病历信息，用于在线复诊时医生参考
住院病历查询接口
获取患者住院期间的病历资料，用于出院后随访或康复指导
既往病史接口
提取患者历史诊断、治疗记录，辅助AI预问诊与健康评估
医嘱信息接口
获取当前医嘱执行情况，如用药、检查、护理等，用于住院患者信息展示与提醒
检验检查系统
接口名称
功能描述
检查申请接口
支持患者线上发起检查申请（如血常规、CT等），由系统自动分配时间并通知医生
检查报告接口
获取检验报告、影像报告等结果数据，供患者在线查阅，并支持链接至在线解读页面
影像调阅接口
获取PACS系统中的影像数据（如X光、MRI等），用于远程会诊与AI辅助分析
药房管理系统
接口名称
功能描述
药品目录接口
获取医院中药、西药、协定方等药品目录信息，用于线上开方与商城推荐
处方审核接口
医生开具电子处方后，药师可通过系统进行在线审方，确保合理用药
中药煎药接口
获取中药煎药方式（先煎、后下、包煎等）、配送状态等信息，提升患者体验
药品库存接口
获取药品库存状态，避免开方后无药可配的情况发生
药品配送接口
与第三方物流系统对接，获取药品配送状态，实现药品送达进度可视化
外部合作系统对接
接口名称
功能描述
第三方支付接口
对接微信、支付宝、银联等支付渠道，实现挂号、药品等费用的在线支付
物流配送接口
对接顺丰、京东、美团等配送系统，实现药品及时送达
AI能力平台接口
对接大模型、图像识别、语音识别等AI平台，支撑AI客服、体质辨识、健康评估等功能
电子签名接口
对接CA认证平台，实现电子处方、诊断报告的法律效力保障

所有接口均采用标准化协议（如JSON/XML格式+HTTPS加密传输），并配备详细的接口文档与测试环境，支持快速对接与持续维护
产品性能设计
该项目其中一方面面向医院和医生，另一方面面向患者，所以有可能出现较大的并发，所以在性能设计上有以下考虑，以确保性能要求。
页面性能优化
尽量减少HTTP请求次数：
终端用户响应的时间中，有80%用于下载各项内容。这部分时间包括下载页面中的图像、样式表、脚本、Flash等。通过减少页面中的元素可以减少HTTP请求的次数。合并文件是通过把所有的脚本放到一个文件中来减少HTTP请求的方法，如可以简单地把所有的CSS文件都放入一个样式表中。
避免跳转：
连接新网站和旧网站是跳转功能经常被用到的另一种情况。这种情况下往往要连接网站的不同内容然后根据用户的不同类型（如浏览器类型、用户账号所属类型）来进行跳转。尽管使用这种方法对于开发者来说可以降低复杂程度，但是它同样降低用户体验。一个可替代方法就是如果两者在同一台服务器上使用Alias和mod_rewrite和实现。如果是因为域名的不同而采用跳转，那么可以通过使用Alias或者mod_rewrite建立CNAME（保存一个域名和另外一个域名之间关系的DNS记录）来替代。
预加载：
预加载是在浏览器空闲时请求将来可能会用到的页面内容（如图像、样式表和脚本）。使用这种方法，当用户要访问下一个页面时，页面中的内容大部分已经加载到缓存中了，因此可以大大改善访问速度。
把样式表置于顶部：
把样式表放到文档的<head /内部会加快页面的下载速度，把样式表放到<head /内会使页面有步骤地加载显示。注重性能的前端服务器往往希望页面有秩序地加载。同时，浏览器把已经接收到内容尽可能显示出来。这对于拥有较多内容的页面和网速较慢的用户来说特别重要。
把脚本置于页面底部：
脚本带来的问题就是它阻止了页面的平行下载。在Internet Explorer中，脚本可能会被延迟，那么它就可以移到页面的底部，这会让你的页面加载得快一点。
剔除重复脚本：
重复脚本会引起不必要的HTTP请求和无用的JavaScript运算，这降低了网站性能。在Internet Explorer中，如果一个脚本被引用两次而且它又不可缓存，它就会在页面加载过程中产生两次HTTP请求。即使脚本可以缓存，当用户重载页面时也会产生额外的HTTP请求。
数据库优化
当数据库单节点的并发处理能力接近饱和，可以通过数据库集群或数据库Sharding提升性能。需要根据业务逻辑和数据库ER图确定Sharding策略，一般分为垂直切分和水平切分。垂直切分的依据原则是：将业务紧密，表间关联密切的表划分在一起。垂直切分后，需要对shard内表格的数据量和增速进一步分析，以确定是否需要进行水平切分，形成一个主表和多个与其关联或间接关联的次表。水平切分是要注意权衡shard的数量和管理维护成本，可以将业务上相近，并且具有相近数据增长速率（主表数据量在同一数量级上）的两个或多个shard合并到同一个数据库上。
从系统数据层逻辑设计架构上看，sharding逻辑可以在数据访问层、xDBC API层、介于数据访问层与xDBC之间的数据访问封装层以及介于应用服务器与数据库之间的sharding代理服务器四个层面上实现。需要根据业务特点和开发框架选择合适的策略。
数据库系统提供很多缓存、索引、自优化处理，提升数据库性能，这就需要根据数据容量、业务逻辑合理分配表空间，保证数据库系统表有充分的空间，但又不过多浪费存储。
索引是提升数据库性能的一个重要手段，但对数据库的插入、更新和删除都会带来索引计算的开销，因此需要根据业务逻辑、数据库表设计，结合数据库系统提供的性能分析工具合理设计和配置索引。
现代的数据库系统一般都提供了功能强大的性能探查和分析工具，可以通过利用这些工具，定位数据库系统瓶颈，通过工具提供的优化建议，结合业务逻辑和数据库表设计进行针对性的优化，提升整体性能。
监控关键运行指标
按照相关接口规范的要求提供指标数据并接入，运行指标反映信息系统运行状况，对应措施列表如下：
运行指标数据项
说明
数据库连接池中可用连接数量
这个数量太小意味着需要加大连接池配置，或者需要调整应用程序和数据库配置以减少数据库操作时间（如优化查询语句，优化索引等）
数据库连接池中泄漏的连接数量
查找应用程序中关于释放数据库连接的部分，确保连接被释放
消息队列中接收到的消息总数
这个数据表示队列中已经接收的消息个数，其数量变化与应用的类型有关
消息队列中没有被取走的消息总数
如果这个数量持续增加，就需要增加消息处理能力，这往往需要调整Message Driven n的数量
任务管理器中已经完成的请求数
记录完成的服务请求数量，服务请求包括http，web service和RMI等，其数量应该随应用系统的运行时间不断增加
任务管理器中等待的请求数
这个数量持续增加或者保持在比较大的水平说明服务器的处理能力不足，需要调整系统的软硬件配置或者优化应用程序
线程中等待的请求数
这个指标反映的系统状态基本同于“任务管理器中等待的请求数”
任务管理器中持续运行的线程数
这个数量表示存在某些请求的处理时间超过了系统定义的正常执行时间，需要确认是否存在这样的长时间交易，需要优化应用程序
http请求数量和字节量
这个数据过大往往需要调整Web Server的相关配置
线程池可用线程数量
这个数量的持续减少说明系统处理能力不足，需要调整软硬件配置或者优化应用系统
事务超时个数
如果这个指标达到一定的数值，需要分析应用和数据库配置是否需要优化
可维护性设计
信息系统具有版本信息、承建单位、功能、组建自述及程序清单，运维人员可日常维护管理。
便捷性设计
易操作性：信息系统页面遵循标准 Html规范，支持包括IE、谷歌等系列浏览器在内的多种浏览器，在浏览器升级时，可以保证以“兼容模式”正常运行；
易学习性：软件操作较为直观易学、有准确和完整的文档，用户使用文档后可以正确有效地完成任务。建立帮助机制，帮助机制中对信息系统功能进行帮助描述的完整程度，系统帮助功能中包含一键提报功能，使用户借助此功能实时提报系统问题或需求到相应渠道。
视觉设计
色彩运用
针对成都中医药大学附属医院（四川省中医医院），在医疗领域传统上与白色、浅蓝色等冷色调关联，这些颜色能够传递出清洁、专业、安宁的感觉。因此，在应用端的主色调选择上，可以延续这类风格，以浅蓝色作为底色，搭配白色文字与图标，构建出清爽的视觉环境。同时，可以巧妙运用辅助色进行提示与区分，如用绿色标注健康资讯、康复建议等板块，寓意生机与希望；以黄色突出重要通知、待办事项等，吸引用户注意力。
图标与图形设计
设计一套专属的医疗图标集，使图标表意直观且风格统一。例如，挂号功能可以用简化的挂号单与笔图标呈现，在线问诊功能可以以对话气泡搭配听诊器图形示意。在展示医疗数据，如体温、血压变化曲线时，应采用平滑、精准绘制的折线图或柱状图，并配上适当的刻度标注与数据标签，方便用户清晰查看健康指标走势。
界面布局
首页布局‌遵循“重要信息优先、功能模块清晰”的原则，将挂号预约、报告查询等高频使用功能置于首页顶部显眼位置，并采用大尺寸按钮，按钮上以简明文字（如“预约挂号”“报告速查”）配合图标展示，方便用户一键触达。下方可以分板块展示医院科室介绍、专家推荐等，利用图片加简短文字说明的形式，吸引用户深入了解。
信息展示布局‌在诊疗报告页面，以表格形式清晰罗列各项检查指标、参考值范围、检测结果等。异常指标可以用醒目的颜色（如红色加粗字体）标注，便于用户快速聚焦健康隐患。医生排班信息可以按科室、日期分栏展示，日期可切换选择，医生姓名旁附上职称、擅长领域简介，方便用户根据自身需求筛选合适的就诊时段与医生。
交互设计
手势交互‌：引入常见手势操作，如左右滑动切换科室页面、上下滚动查看诊疗历史等，以贴合用户移动端操作习惯。
反馈机制‌：点击按钮、提交表单时应有即时微动画反馈，如按钮短暂变色、出现加载小圆圈等，告知用户操作已接收正在处理。操作完成后应给予成功提示（如弹窗显示“挂号成功”“报告已上传”）或失败警示（红色字体说明原因），以增强交互的质感与确定性。
响应式设计
确保在手机、平板等各类移动设备屏幕尺寸下均有良好的显示效果。可以利用CSS媒体查询技术，对小屏幕设备（如手机）优化为单栏布局，功能按钮紧凑排列、文字大小适配阅读；对大屏幕设备（如平板）转为多栏展示，拓展信息展示量。
情感化设计
温馨提示：在诊疗流程的关键节点添加温馨提示语，如在患者预约手术前弹出“手术前请放松心情，我们医护团队将全力保障您的健康，如有疑问随时联系我们”；在康复阶段展示“坚持康复训练，每天进步一点点，您正迈向健康生活”，从心理层面给予用户关怀支持。
综上所述，应用端视觉设计方案应综合考虑色彩、图标、布局、交互、响应性、无障碍性以及情感化等多个方面，旨在为用户提供专业、便捷、温馨的医疗数字化服务体验。
系统详细设计
数据洞察分析
患者画像分析
依托短视频平台海量用户行为数据与健康内容生态，构建动态化患者画像体系。通过解析公域行为数据（含内容播放、互动等）、兴趣标签数据、地域特征数据，从内容消费层级（基础、互动、传播层）、健康风险预测、地域化策略等维度，精准捕捉与分层管理中医健康需求，为个性化健康干预方案设计提供支撑。
中医健康食品洞察分析
基于短视频平台健康食品内容传播规律与用户消费行为数据，构建 “趋势洞察 - 产品研发 - 精准营销” 闭环。借助内容传播数据、用户生成内容、消费行为数据，从爆款要素拆解（内容形式、情感共鸣）、品类创新方向（场景细分、跨界融合）、传播策略优化（KOL 矩阵、话题裂变）等维度，指导中医健康食品品类创新与市场渗透，抢占“药食同源”消费新场。
互联网门户功能设计
患者端功能设计

专属门户
中医特色门户
个性化特色设计，彰显中医五行色系，凸显中医名家与特色科室，传达中医养生理念。将中医的“辨证论治”理念转化为数字化交互逻辑，确保专业性的同时，利用现代技术实现“千人千面”的中医健康服务。门户网站设计将融合关键功能，核心功能及亮点将得到突出展示，同时满足服务闭环的需求。
适老化设计
医疗服务流程的优化与适老化调整：简化了挂号、查询检验报告、查看订单等医疗服务流程的功能模块；
界面风格的适老化设计：字体大小增加，用户界面设计更为突出；
针对老年人的智能语音功能：利用先进的语言模型快速识别并导航至相关功能；
老年人版的亲属代办功能：用户能够向亲属发送代办事项，亲属在接收到代办短信后，能够迅速通过微信小程序查看并处理代办事项。
首页待办中心
根据用户的使用习惯，系统能够动态地在首页展示其待办事项，以便用户能够迅速定位到相关服务。这些待办事项包括：
进行中的专病服务包；
待支付的问诊订单；
待接诊的问诊订单；
正在沟通中的问诊订单；
待取药的订单；
待进行的检查事项；
待评价的问诊订单；
待使用的复诊权益；
待使用的加号服务。此外，待办事项组件支持用户进行收缩与展开操作。
AI+智慧就医服务
AI客服
用户可向AI询问使用过程中的问题，并可转到人工客服。客服问题知识库可在后台维护。具体能力包括:
医学健康知识问答，医学健康知识问题的咨询解答；
医院业务知识问答，支持医院自身业务知识内容的问答；
服务分发，根据患者问询意图将医院服务进行服务的分发；
语音识别，支持普通话/英语语音识别；
数据统计与分析，支持数据统计，如患者常问的问题等。
AI分诊导诊
通过AI医疗模型结合医院科室及医生画像，提供精准分诊导诊算法。具体服务及能力报告：
AI就医意图识别引擎，用于就医接待，基于AI模型对于患者就医意图进行分析，判断是否需要提供就医推荐服务，以及用户所需的就医服务的引导；
AI患者病情分析引擎，用于病情描述识别，通过AI医疗模型对患者病情进行问询，以及对患者症状、疾病的分析，完善患者画像，形成病情分析小结；
AI就医推荐引擎，用于就医方向指导，根据患者病情分析和报告解读结果，指导患者可能的就医方向；
根据引擎能力和算法，推荐就诊科室、推荐对症专家，根据AI病情分析引擎对患者症状/主诉的分析，结合各科室诊疗范围和科室内医生画像，智能判断患者可对症预约的诊疗科室和合适专家；
就医服务引导，根据患者病情分析及就医推荐的机构和医生所开通的服务，为患者提供挂号、咨询、复诊等服务的引导。
AI预问诊
患者面诊医生前，通过模拟中医临床医生问诊，如智能辨证、数据化体质辨识、个性化调护方案等帮助医生提前采集患者一诉五史病情信息，自动生成标准化预问诊小结，帮助医生提前了解患者病情信息并对接院内电子病历系统录入病例信息。具体能力项包括：
对话式交互功能：对话式交互询问患者病情信息，模拟真实对话流；
预问诊小结：生成标准的结构化预问诊小结，支持医生增删改查，支持直接写入电子病历；
AI病情分析：支持结构化展示病情，辅助医生提供诊断建议；
科室模板配置：支持不同科室模板配置；
症状描述辅助功能：症状描述支持通过图示、辅助文字来关联说明，以帮助患者更好理解症状。
智慧门诊
门诊挂号
挂号界面
患者可以查看在掌上医院的挂号记录、候补记录，也可以查看其他渠道的挂号记录。
选择院区后可查看到相应的就诊日期、科室以及医生。该页面将自动呈现患者过往的挂号记录。
支持在挂号详情页面，进行中英文的切换，方便国际友人将英文界面切换成中文展示相关信息给院内相关工作人员。
预约挂号
提供全方位的院区导航服务，患者可以查看各个院区距离当前的距离、导航至院区的路线，助力用户全面选择适合自己的挂号院区。
支持预约线下多学科诊疗（MDT）号源，整合各科室专家资源，支持患者自己选择需要参与mdt的科室及专家级别（正高、副高），并可根据患者选择参与医生不同，实时计算挂号价格。确保患者能够及时获得由资深专业团队提供的全面综合评估及个性化治疗建议，从而提高诊疗效率和治疗效果，为患者提供更优质的医疗服务。
支持挂号候补功能，一旦有号源因故临时释放，系统将立即启动自动通知机制，为候补名单上的患者优先提供挂号机会，确保每一位患者都能公平、及时地获得就诊机会，极大缓解挂号难的问题。
当日挂号
支持当日号源的查询与筛选功能，用户可以通过系统查看当日可用的号源信息，并根据自身需求进行精确筛选，确保能够快速找到合适的就诊时间。
支持根据个人需求的便捷性，灵活选择最适合自己的科室、医生或名医团队。系统提供详细的医生介绍、专业特长，帮助用户了解每位医生的优势，从而做出更为明智的选择。
提供当日挂号在线支付的便捷服务，患者可以通过手机在线完成挂号费用的支付，无需亲自前往医院现场缴费，极大地节省了时间和精力，提升了就医体验的便捷性和舒适度。
预约取号
患者完成支付后凭借支付凭证进行取号，支持多种取号方式，避免了传统排队等待的烦琐和时间浪费。
预约退号
用户可以轻松查看和管理自己的挂号信息，并根据就诊需求和就诊计划选择退号操作。
仅针对尚未进行看诊的挂号，系统支持用户进行退号操作。
退号要求根据医院要求，对患者进行提醒和提示。
退号记录会计入预约规则及风控规则，减少频繁退号或恶意占号风险。
退号成功或失败均及时对患者进行提醒，并通过消息通道进行通知。
门诊缴费
门诊结算
系统支持对挂号、线上复诊、药品、检查检验等项目费用进行线上支付。
支持选择就诊人，（要求绑定病案号）；
支持查看待缴费的项目；
支持.在线自费支付缴费；
支持查看缴费记录。
医保结算
支持与HIS及医保系统对接后实现在线医保结算。
信息查询
支持药品查询、费用查询、医院介绍、就医指南查询、发票查询、院内预留信息查询等。具体如下：
查看药品目录及药品；
查看门诊支付费用信息及费用详情；
支持查看医院介绍；
支持查看院内导航图；
支持查看院内门诊、住院就医流程等就医指南；
支持查询电子发票；
支持修改用户在医院保存的预留手机号、地址等信息。
排队候诊
在线签到
支持患者在到达医院的范围内时，通过手机微信小程序签到并获取排队号码，减少患者在医院等待中的时间。
自助开单
检查检验服务
患者可以根据自己的需要自行选择和开具相应的检查项目。
支持后台配置，根据诊疗规范及管理要求允许患者自助选择的检查项目的范围。
检查检验报告查询
支持患者查看待预约的检查项目、在线预约检查时间，查询预约记录等；支持患者在线查看检查报告、检验报告、其他类型的报告（需系统对接支持）。
检查检验报告解读
支持患者在查询报告后链接至在线报告解读页面。
智慧住院
院前服务
入院预约
支持患者住院预约功能，便捷填写个人相关信息。
支持查询医生开具的住院证详情，包括姓名、年龄、性别等个人信息，以及住院病区的具体信息。
预约成功后，可实时查询预约状态。
入院办理
患者可以查看已开具的住院证信息，包括住院科室、床位安排、主治医生等详细信息，方便提前做好住院准备。
支持用户在线完成住院登记手续，避免了繁琐的线下排队流程。
支持在线缴纳住院预缴金的功能，可便捷地完成费用支付，无需前往医院窗口排队。
入院须知
1.根据医院提供的内容，为入院患者展示住院须知、入院流程、注意事项。2.支持患者查看自己的主管医生、主管护师。
住院预缴
实现患者通过移动端在线充值住院押金，支持微信支付方式。同时支持查询历史充值明细信息、预缴金余额信息。
院中服务
住院一日清单
实现患者查询指定日期住院费用总额及费用分类明细清单，包含支付类型及明细，以便患者能够详细了解在特定日期内所产生的所有住院费用总额，以及这些费用是如何分类的。
住院清单查询
患者可以通过系统方便地查询自己在住院期间的各项费用清单，包括但不限于每日的诊疗费、药品费、检查检验费以及其他相关费用。
院后服务
出院结算
支持自费患者能够在线上轻松查看自己的出院结算信息。支持患者进行预交金的补缴。
出院小结
支持查阅出院小结文档，其中详细记录了患者的出入院时间、整个治疗过程的描述以及治疗所取得的具体结果等关键信息。
出院带药
支持用户方便地查看出院时所带药物的详细清单，帮助患者或其家属更好地了解出院后需要继续服用的药物信息，确保用药的准确性和连续性。
病案复印
支持提交病案复印申请。
支持选择就诊人（要求绑定病案号）；
查询可复印的病案记录；
选择复印份数、取病案方式（邮寄或自取）；
支付复印费用；
查询病案复印申请记录，查看排片、邮寄状态等。
主管医护咨询
支持查看本次住院对应的主管医师，并可发起咨询服务。
互联网医院
在线诊疗
医护查询
支持根据名称、科室、疾病搜索医生，查看医生的基本信息、专业擅长、提供服务、平均响应时长、用户评价等信息。同时，还可以查看医生团队的详细信息，了解各医生的专长和团队构成，帮助用户找到合适的医生、团队。
医护智能排序
支持通过多种维度，对医护进行智能排序，包括以下维度：
医生服务量；
接诊率；
接单响应时长；
服务评价；
职称；
服务开通情况；
简介完善程度；
医生标签。
在线复诊
支持用户发起在线复诊，选择期望开具的药品、检查检验项目，填写复诊内容，与医生进行复诊沟通。
在线咨询
提供多样化的咨询选项，以满足不同用户的需求。功能包括：
团队咨询：用户可以直接向医疗团队发起咨询，团队成员可以团队名义共同协作处理患者的咨询。
图文咨询：通过文字和图片与医生进行在线咨询。
电话咨询：提交咨询后，可接到医生的虚拟通话，与医生电话沟通（支持到达医生约定的电话时长后自动挂断）。
视频咨询：提交咨询后，可接到医生的视频呼叫，与医生视频沟通。
极速咨询：用户可向科室发起咨询，科室的医生可以抢单回答，保证快速响应。要求在沟通过程中支持：1、网址识别；2、二维码识别；3、消息撤回、消息引用；4、支持全屏输入模式。
咨询转复诊
医生可以根据咨询情况将患者转为复诊，以便进行更深入的开单等延续性服务。
预就诊专区
支持患者通过预就诊专区，找到复诊医生，在线下就诊前进行线上预就诊，提前开具需要的检查检验单。
专科门诊
支持开展在线专科门诊，患者可以查看后台设置的各个专科值班医生，并向医生发起在线复诊。支持科室筛选，上午门诊、下午门诊、夜间门诊的分开查看。
在线义诊
支持查看义诊活动介绍、参与医生，支持义诊服务、义诊价格、义诊名额等，并在符合条件时参与义诊活动。支持提前查看义诊活动，并订阅义诊开始通知。
场景化咨询
支持提供专门场景的咨询专区，如用药咨询、护理咨询等。医患在线交流、常见病辅助诊断、个性化养生方案推送。
药事服务
处方查看
用户可以查看处方内容，包括药品信息、医生的签名等。
在线购药
系统支持患者在线缴费及取药功能。系统支持患者可通过线下药店取药、药品配送方式取药。支持：1、患者点击购药，选择取药方式，填写收货信息；2、支付相关药品/物流费用；3、患者查看药品物流信息；4、患者也可以选择到院取药，在支付费用后查看取药相关信息。
自助购药
患者自助选择需要的中药茶饮、院内制剂、OTC药品和养生保健用品，提交医生开方后可购买。
特色服务
全病程管理
病种管理中心
可对患者端展示的病种管理中心，支持查看医院正在管理中的病种以及病种对应的管护计划，患者可通过管护计划内容页申请入组。对于符合条件的申请患者，支持自动或人工审核通过。
专科服务中心
患者可查看各科室提供的专病的服务包，患者根据病情需要可选择对应科室及医生提供管护服务，打通在线支付。支付后，可自助使用服务内容，同时支持自动入驻到对应的付费专病管护服务内。
支持查看后台配置的专科服务中心banner、参与医生、团队等信息。
数字人管护师
签约到数字人管护师的，可通过数字人管护师对患者发送管护计划、服务内容、异常预警的处理方案。数字人管护师的形象IP可根据医院定制样式展示。
管护计划查看
纳管到专病计划后，可查看完整的管护计划，包括管护责任人、管护内的服务及监测项目，如设置可退组流程，支持用户取消签约入组。
专病计划内日程
可查看专病计划内自动触发的管护日程，包括宣教内容、随访通知、专病风险评估、问卷反馈、就诊提醒、用药提醒等管理路径内配置的服务项目。患者需根据配置的时间等日程有效期要求进行反馈，反馈结果会实时同步到管护责任人，并支持后台查看和数据统计。
异常监测与处理
对计划内配置的异常监测内容，包括数据指标异常、反馈信息异常等内容进行实时的监测，发现后自动上报。如果计划已配置异常处理方案，支持异常方案自动通知到患者。
专病个案管理
患者可根据入组的病种和管护计划配置的要求，查看、填写对应的专病管护档案，档案内根据后台配置字段展示患者基本信息、病史信息、检查检验等维度的数据。
数据指标监测
支持病种内需要日常监测的数据指标的采集，包括患者手动上传和接口自动同步两种形式。数据指标包括体温、体重等健康指征数据和血压、血糖等疾病风险指标。支持阶段性的数据统计与分析，呈现方式可支持图表形式。
在线沟通
支持医护人员在专病管理后台，对于重点关注患者、异常预警患者进行主动在线随访，患者可以在全病程管理中心查看到医护人员发送的消息；在医护人员设置的可回复条件内，可通过回话回复医护的消息。
健康指标
支持患者上传血压、血糖、BMI能核心健康指标，异常情况支持咨询数字管护师。
三师共管
患者加入管理计划后，自动创建患者专属健康管理医护群，实现三师或者五师共管患者，患者可以在群里进行咨询。
中医延续护理
护理区域门户
支持打造区域化中医护理服务平台，以省中医院为牵头机构，支持多机构入驻，协同运营、统一培训、规范管理的服务体系。护理区域门户设计会结合重点功能，核心功能及亮点突出，又能满足服务闭环，包括护理咨询、就医陪同、护理上门、中医特色服务等服务类目。
服务查找
服务展示：支持按照服务分类展示服务项目，便于患者查找所需的服务项目，并可展示最近服务、推荐服务、专家和机构。服务搜索：提供全局和项目名称搜索功能，便于患者查找所需的服务项目或商品。服务查看：服务内容显示及查看，服务显示服务项目介绍、服务项目原价、优惠价、已预约数量、预约须知、服务项目相关的用户评价等信息。
服务下单
支持预约护理人员上门服务，预约时需明确本次服务需求，例如服务时间、服务地址等，便于患者本人/患者代理人快速下单；1.支持自动定位和手动填写地址，并可线上签署《知情同意书》。2.系统支持在预约过程中未在指定时间内完成支付，自动取消服务预约。3.支持调用百度、高德等地图组件，计算服务地点与医护执业地点之间的路程距离，以确认上门服务路程费用。
订单管理
首页支持滚动展示用户下单动态；可查看所有护理服务的记录，包含服务状态、服务详情、服务费用等信息；对于已完成服务，支持对护理服务进行评价，包含文字评价和星级评价；支持再次预约，服务项目、服务医护等预约信息不需要再次填写，方便快速预约操作。
护理活动动态
开屏活动：用户开屏活动推送，点击后可跳转至具体活动页面或服务购买页面；下单动态：首页滚动展示用户下单动态。
关注医护
支持用户关注医护人员，关注后可从“我的关注”中快捷找到关注护士
账号及安全
身份管理：可通过获取用户授权信息创建账号，支持对就诊人进行实名认证，并可绑定多个就诊人；信息管理：支持用户个人信息管理，如绑定微信账号，服务地址管理，支持个人信息修改；在上门护理服务中，支持通过核验二维码、手机号对服务对象身份认证，以确保服务的正确进行。
支付管理
系统支持耗材费用、路程费用、服务费用的自动结算和线上支付。在上门护理服务中，支持服务过程中产生的追加订单、超时、服务费等进行支付，并支持退款
智慧服务向导
支持重点消息手机短信、微信服务通知双提醒；支持进入订单详情查看不同状态的提醒。
日间手术
系统支持生成日间手术中心二维码，引导用户扫码进入日间手术中心；支持患者查看日间手术术前、术中、术后各个状态下，应该办理的流程引导，及使用相关服务，如看主管医生、术前事项、术中手术进度、术后服务等。
中医专区
AI中医体质辨识
体质辨识
用户跟着AI提示进行回答或上传必要信息（如舌诊照片等），AI分析并告知用户属于中医九种体质中的哪一种（比如气郁质，平和质等）。
智能体质报告
系统生成一份用户的专属体质报告，告诉用户测评后的中医体质、体质特点及要注意的健康教育指导包括饮食、运动调理建议等。
AI智能推荐
根据用户的体质辨识结果并结合用户的基本情况，及AI轻问诊的禁忌提问，AI智能推荐适合用户的茶饮处方，用户能看到推荐理由与使用建议，方便用户选择购买。
体质评测
用户根据系统引导完成体质辨识评测，AI智能推荐适合用户的茶饮处方，用户根据评测结果及智能推荐结果购买茶饮处方。
中医健康商城
中药茶饮
支持患者自主选择需要的中药茶饮，提供直接购买或咨询后购买两种方式。
查看健康单
支持患者查看开具的健康单，内容包括营养食品、养生茶饮及康复建议单等相关健康信息。
中医名医工作室
名医工作室
建立以名中医为核心的线上团队咨询服务模式，提供知名中医专家团队在线问诊服务，打造名中医线上服务口碑。
中医健康宣教
健康宣教
支持用户通过主动检索和分类查看的方式进行图文和视频健康知识宣教的查看。
护理宣教
支持宣教内容中推荐相关上门护理服务项目，点击后跳转至购买页面。
健康科普
查看病种配置的健康科普，查看日常推送的患教科普文章。
便民服务
院内导航
院内楼层分布查询：提供院内楼层、科室分布查询，允许用户通过查看医院内部楼层的静态图片、楼层分布信息，如院内楼层示意图等，从而轻松地找到他们所需要前往的目的地。
满意度调查
支持针对门诊、住院患者提供不同调查问卷。患者可以通过系统方便地查看并填写满意度调查问卷，从而为医院提供宝贵的反馈信息。
客服服务
电话客服
支持24小时线上客服答疑，同时支持工作时间拨打座机联系客服。
服务查询
服务条款查询、平台信息介绍等信息。
就医指南
提供各种类型的就医指南和须知等重要信息，帮助就医患者更好地了解就医流程和注意事项。就医指南的分类，可根据配置分类，如按照门诊就医指南、住院就医指南，以便高效地利用医疗资源。
个人随访服务
患者提交资料：在患者入院时引导患者关注微信，提交个人信息加入随访流程；患者可查看健康档案；
个人档案：支持患者查看和手动提交个人信息，如联系方式、身高、体重居住地、工作情况、抽烟情况等个人信息。
报告记录：支持查看患者的检查检验报告记录。
随访任务：通过时间轴展示的方式，查看曾经接收到过的随访计划和未来一周内的随访任务。支持查看随访任务详细内容。
消息推送：根据业务场景联动选择支持健康宣教、满意度调查、服药提醒、预约挂号、检查检验下单功能的推送，支持微信、短信等推送方式。

个人中心
账户管理
系统支持注册、登录、登出和修改账户。
就诊人管理
系统支持多种方式实名认证，绑定多个就诊人和关联院内病案号、就诊卡号功能，并可查看和编辑个人资料，查看平台业务使用记录（如挂号记录、咨询记录、处方记录、开单记录）等。
业务记录查询
支持患者在个人中心查询挂号记录、诊疗记录、处方订单记录、个人权益、加号凭证等。
消息中心
支持通过消息中心向用户发送各类消息提醒。
意见反馈
支持患者发起意见反馈，患者可以通过线上平台轻松提交他们的意见和建议。
医护端功能设计

医生工作台
在线诊疗服务
在线咨询
团队咨询：医生可以作为医疗团队的一员，响应并处理患者的咨询。
图文咨询：医生可以通过在线平台接收并回答患者通过文字和图片提交的咨询。
电话咨询：系统允许医生在预定时间内通过隐私电话（不泄漏医生个人手机号）与患者进行电话沟通，支持到达设置通话时长自动挂断功能，确保通话时间控制在约定范围内。
视频咨询：医生可以通过视频呼叫与患者沟通，更直观地了解患者状况。
极速咨询：支持医生对于面向科室发起的咨询进行抢单回答，这种机制确保了患者问题的及时处理。
咨询转复诊：医生有权根据咨询内容判断是否需要为患者升级为复诊，以进行进一步的开具检查、处方，从而提高治疗的连贯性和有效性。
AI病历书童
通过AI模型调用及算法优化，为医生根据咨询及问诊内容自动生成格式化病历，支持医生引用、修改。
在线复诊
提供与患者在线复诊的服务，支持医生写复诊意见、开处方、开检查检验单等。
复诊患者权益
向复诊患者提供免费咨询一次的权益二维码，方便医生为复诊患者提供延续性服务。
患者档案
查看患者的院内检查检验报告等档案。
智能消息设置
支持医生对消息进行多维度设置，包括：
微信小程序消息通知权限的检测与开通；
消息免打扰时间段设置；
支持设置沟通中消息红点在已读后即消除或回复后才消除两种模式；
支持设置新订单通过微信或短信+微信提醒两种模式；
支持设置新订单在自定义时间未接诊提醒，最多支持设置5个时间点的提醒；
支持设置消息在自定义时间未回复的提醒，最多支持5个时间点的提醒，要求支持通过AI智能分析过滤无需医生回复的消息；
支持设置是否开通每日待处理日报，包括中午、晚上两个节点的选择。
开单服务
开检查检验
医生给患者开具检查单、检验单，线上选择开具项目、部位等信息，支持开单套餐的维护与开具。
开加号凭证
支持医生给患者开具线下就诊加号凭证。
记录查看
查看开单记录，开单项目、开单的患者等信息。
药事服务
开具处方
支持医生给患者开具处方单（支持西药、中成药），电子签名，支持处方的智能拆分。
处方审核
支持药师在线审核处方。
处方管理
支持查看处方记录，支持处方的召回。
医生工具
数据统计
支持医生查看服务量、评价等数据统计。
医护名片
提供医护人员对应的二维码名片等信息。
发布公告
支持医生发布公告，公告呈现在医生主页显示，公告支持有效期。
医生随访工具
随访计划：支持查看医生所在组的全部随访计划；支持查看计划内全部患者的随访任务；支持查看指定患者的随访计划内容；支持关注患者；支持对患者发送消息；
待审核：支持对定期检查，定期检验，慢病评估报告审核单的审核与作废操作；
我的消息：支持消息发送功能，可管理医生对患者发送过的历史消息内容；
关注患者：支持管理医生在计划中关注的患者。
患者管理
患者管理中心
患者中心
支持对患者的档案、基本信息、重点指标、病史、住院记录、门诊记录、体检记录、检查报告、检验报告等信息查看，并可通过列表展示。
患者分组
对患者自主提交的入组申请进行审核操作；支持按分组维护查看患者列表；可对患者进行维护标签操作。
患者日程管理
可按事件类型、病种分组、日程状态等维度进行日程筛选，能查看并处理日程详情，包括填写问卷、查看宣教、查看通知提醒等。
医患在线沟通（1V1）
对于患者、医生之间进行线上沟通，沟通形式包含私聊、群聊，内容包含图文聊天、发量表、发宣教等等；可维护常用语。
医护群聊（1Vn）
患者入组后可以自动加入一个专属全病程健康管理群，患者在群里进行咨询，由医生、护士或个人管理师共同提供服务。支持发图文、量表和宣教内容。
患者异常事件处理
患者异常事件预警
医护人员可以在小程序接收到在管患者指标或者随访事项异常提醒，并支持通过在线沟通的方式对患者进行健康随访干预。
医患关系
医患关系设置
支持医生设置：1、是否接受患者报到，报到相关提示内容，报到成功后欢迎语，报到成功后是否赠送沟通条数；2、是否在患者咨询问诊后自动加入管理，可以设置仅对扫描复诊二维码的患者问诊后自动加入管理。
我的粉丝
支持查看关注自己的粉丝新增数量、粉丝列表，包括粉丝关注时间、关注渠道，是否与自己发生过在线诊疗行为。可以主动将粉丝纳入管理。
报到患者审核
支持查看主动发起报到申请的患者、患者资料，支持进行审核拒绝，或审核通过后纳入管理
患者入组审核
医护人员可以在小程序端查看申请加入计划的申请并支持在线处理。包括同意申请和填写拒绝理由后拒绝申请。
纳管患者
支持将患者纳入管理，并分组标签，对纳管患者可以查看档案、主动发起沟通触达。
群发管理
支持医护人员在小程序端对患者进行批量发送提醒、随访、评估、宣教内容。支持查看群发内容的记录、发送成功的人次、是否已读情况。
重点关注
按当前账号的数据权限，展示服务患者总人数等重点关注数据。
数据分析
支持患者管理、患者统计、管理计划等。
消息提醒
支持查看系统消息、日程消息、私聊、群聊消息等，并打通微信小程序服务消息，及时通过微信端进行消息提醒。
个人中心
账户管理
系统支持注册、登录、登出和修改账户。
个人信息管理
支持对个人信息的更新，如介绍、擅长等，以确保患者能够获取最新的专业背景信息。医生可以查看患者对其咨询服务的评价，这有助于医生自我评估和提高服务质量。提供医生名片，包括医生的专业信息，医生二维码，便于让患者快速找到医生。
平台服务介绍
提供客服联系方式、医院简介及相关条款的查看，以便患者了解平台信息。
运营特色服务
医生活动
支持医生查看年终配置的总结一封信内容，包括个人的年终数据、文案，支持医生生成自己的信件、分享信件、查看别人的信件等。
AI医生助理
医生端设置AI医生运营助理，为医生提供基于AI模型和运营知识库为基础的运营策略、产品使用等服务支持，可了解最新的运营活动、产品操作手册、运营数据等。
护理上门（护士端）
服务过程管理
服务距离查看
支持护士查看订单距离，并对导航点进行第三方导航。
联系患者
支持护士通过虚拟号码联系患者。
服务管理
支持护士现场签到/签退，并监控服务时长；若超时护士可选择加收超时费；支持护士根据实际情况进行现场加单；支持医疗垃圾处理记录，支持拍照上传处理结果。
修改服务时间
支持护士与用户确认后，修改上门服务时间。
培训管理
支持护士线上报名培训活动，查看培训信息，并查看已获取的证书。
护士安全
身份识别
支持护士签到时通过手机号、扫码方式进行患者身份核验。
一键报警
支持护士一键报警，将报警时经纬度发送给管理员。
业务设置
业务状态设置
支持对护理服务项目进行开启和关闭设置，仅开启中的服务能接收到患者的预约。
排班设置
支持自动排班和手动排班，仅排班时间能接受服务预约。
订单处理
接取新订单时通知护士及时查看；支持护士查看患者订单基本病情信息，评估后可选择接单或转单、拒单；支持护士在抢单大厅进行抢单；支持护士对订单状态进行变更（如出发操作）。
账户管理
注册及登录
护士资质认证，具备相应工作经验以及职称的护士方可上岗服务，支持护士资质的在线提报及审核。
证书查看
护士通过某资质培训后产生的培训证书支持在线查看、导出。
名片管理
支持生成个人二维码，方便进行推广，扫码后直达名片主页。
信息管理
业务信息查看
支持护士查看自己的订单信息、用户评价等。
服务留痕
服务小结
支持护士根据各个服务项目配置的表单填写服务信息；支持填写医疗垃圾处理记录。
四级联动
远程会诊首页
查看远程会诊首页，含全部会诊记录。
会诊发起
支持填写、补充会诊申请单；支持不同机构医护间发起远程会诊；支持不同场景下发起远程会诊服务，如居家护理场景；支持选择指定会诊专家；按指定条件匹配会诊专家；提交即时、预约会诊申请；支持取消会诊。
支付费用
支持微信在线支付会诊费用；支持生成付费二维码，由他人手机扫码支付费用。
会诊评估
支持同意会诊、拒绝会诊操作，支持输入拒绝理由；支持调整会诊时间。
视频会诊
支持创建诊间，在诊间进行图文、语音沟通；支持选择成员发起视频会诊；支持视频窗口缩小放大；视频进行中支持邀请成员；支持会诊意见填写。
我的会诊
支持会诊评价；支持查看我的全部会诊。
会诊管理
支持会诊团队维护，包括团队类型、性质、团队人员、团队会诊价格；支持会诊医护维护，包括医护介绍，是否接收会诊，会诊价格维护。
会诊模板管理
会诊的模板维护，应用到科室、机构；发起人评估模板维护，应用到科室、机构。
会诊订单
全部会诊订单记录及订单详情查看。
综合管理后台功能设计

数据驾驶舱
数据驾驶舱
围绕互联网医院业务运营核心指标，构建可视化数据中枢，实现动态监测与智能决策支持：具体功能及要求包括：
主要业务及当前成果展示；
各科室&医生服务排名情况；
用户流量及使用监测；服务字段以业务系统数据为基础，驾驶舱设计满足医院管理需求。
业务数据分析平台
运营数据分析
支持用户及流量分析、业务数据分析、医护资源、经营情况数据分析。
用户分析：从用户角度出发，统计分析患者情况，如患者人数、患者年龄分布等。
医护资源分析：支持查看医生的行为数据分析，如医生接诊排名、平均评分、接诊速度等。
业务数据分析：支持按月、年统计不同业务类型的统计和分析数据，根据医院开展的业务来进行统计，如图文咨询、电话咨询、在线复诊、开单统计、预约挂号等。
业务数据分析
能够根据医生和科室的不同，对不同类型的业务进行详细的统计，如咨询、预约挂号、处方药品订单等。
支持对机构业务使用情况进行统计，如咨询、预约挂号、处方药品订单统计等。
支持按日维度分析，业务订单的趋势，如咨询申请量、咨询完成量、咨询收入趋势、预约挂号量、处方开方量等。
业务运营管理
预约挂号管理
支持挂号订单的查询与管理（包括挂号就诊人、就诊时间、预约/挂号科室等订单信息、支付信息记录）；支持挂号业务数据的基础统计；支持挂号业务的规则、须知的文字提示配置。
检查检验管理
支持检查检验订单的查询与管理（包括开单医生、开单项目、检测人信息等）及预约订单管理；支持检查检验开单数据的基础统计（按科室、开单人统计）；支持检查检验业务的配置（检查/检验项目管理、开单套餐维护、自助开单人员病历配置等）。
在线缴费管理
查询患者在互联网医院上门诊缴费的记录。
在线诊疗管理
支持专家咨询（图文、电话、视频）、极速咨询、团队咨询、在线复诊订单的查询与管理（包含患者信息、订单信息及沟通记录等），支持发起订单的退款；提供咨询订单履约明细的数据统计；支持极速咨询配置（服务规则、价格、科室/病种创建、接诊医生池管理）、团队诊疗配置（团队分类、成员创建、分成管理）以及便捷门诊排班管理。
院前服务管理
支持院前中心配置（包括配置院前节点事件、服务等，生成用于患者扫码的院前中心二维码），并可查看院前中心使用记录（使用过院前中心的患者使用记录）。
日间手术管理
支持日间手术中心配置（包括配置日间手术节点事件、服务等，生成用于患者扫码的日间手术中心二维码），并可查看日间手术使用记录。
药事服务管理平台
药事服务管理
支持机构开方数据的基本信息维护与参数配置，支持管理诊断、药品目录、商品目录、商品类目等信息的管理，支持维护患者端药品分类与信息（包括药品分类管理、药品信息与说明书维护）；支持查询药品订单记录（下单时间、收货信息等）；支持查看药事相关订单统计数据。
处方查询管理
支持查询处方信息（开单医生、开单内容、患者信息等）。
运营活动管理
运营活动管理
运营位管理
包括用户端、医护端banner等运营位置配置（包括banner图片上传，跳转小程序地址、h5地址、外部地址管理），设置首页大的专区图片（大的广告位，要求可以通屏展示，并可以配置搜索按钮、通屏图片、进入主按钮样式），及相关跳转的配置等。
活动搭建工具
支持自定义上传活动图片，并进行多个热区（自由选择点击区域）设置，支持不同热区跳转至不同功能。支持对各个热区的点击量进行数据统计。
义诊活动工具
义诊活动的配置，包括活动名称、介绍，活动有效期，活动参与医生、团队，参与价格、名额等。可以配置义诊活动排班表、接诊时间。
年终一封信配置
支持配置年终一封信多页活动。支持自定义多页的背景图片、文案，支持40+变量的自由引入。支持开启页、内容页、分享页的配置。支持对开启、分享活动的医生的数据统计。
运营内容管理
评价管理
通过系统自动审核或人工审核方式进行审核评价，可以快速查找、筛选评价，可以根据差评找到相应服务订单进行诊疗过程追溯。
意见反馈
用户反馈内容、反馈时间查看，对用户反馈进行跟进处理等记录。
就医指南
对就医指南的内容进行配置和维护。
用户及宣教管理
宣教管理
宣教板块管理
支持管理员维护宣教的一级分类。
宣教内容管理
支持管理员发布、修改、删除查找宣教内容，支持宣教内嵌入护理服务购买链接。
后台用户管理
包括对管理后台的角色、权限及账户配置。
患者用户管理
支持患者端账号管理（注册时间、渠道、实名认证等信息），以及就诊人管理（就诊人注册时间、姓名、证件信息、住址、病案号等信息）。
公共支撑平台
终端配置管理
支持配置小程序首页主模块、常用服务、门诊服务、住院服务模块的图标链接和主题样式（字体、按钮、图标颜色），维护平台公告及用户协议、隐私条款等内容。同时支持监管平台上报管理，包括推送订单管理、药品备案及业务监管备案设置。
机构管理
基础信息管理：支持对医院、科室、医生、护士等进行管理，并按照医院组织进行对应信息配置。
业务管理
医生业务配置管理：支持图文、电话、视频、极速咨询、团队咨询等业务的配置，包括开通的医生、团队，开通的价格、服务具体参数等。
支付管理
流水记录
查看平台收费的流水记录，可用于财务对账及医生结薪等场景。
退款管理
支持管理员查看退款情况、理由，批准退款操作。
专科专病患者管理
AI患者发现
通过AI模型对接入的患者进行详细病情和病历进行分析，实现患者按疾病自动分组打标，筛选出专病待管理患者。基于用户风险画像，提供定制化中医治未病干预路径。
病种管理
病种管理
管理员可配置各科室需要进行管理的病种分类，用于创建专病管理计划、以及患者端的展示。
指标管理
支持指标的分类、指标设置和指标组的设置和编辑。其中指标项包括文本、数字、选项、日期/时间、图片等多种类型。同时可设置指标组模板，提高配置效率。
专病档案
可通过对接医院的信息系统内的指标和后台维护的病种指标，建立专病的档案，包括基本信息、病史信息、检查检验信息、现病史等维度，支持通过模板快速配置，支持患者端填写和后台维护两种方式。可用于患者建档、健康档案维护、专病档案维护等业务场景。
专病计划管理
智能管护路径
按病种创建管理计划和管理路径，通过打通院内患者数据，可按指标和就医流程实现精准患者自动入组、自动管护、异常自动上报、异常自动处理的全流程；支持病种管护计划签约到数字智能管护师，有专病数字人管护师进行管护任务的下发和信息回收。
管护服务事项
智能管护路径内支持配置宣教、文本提醒、表单等形式，并支持通过配置链接推送咨询、挂号、开单、预约检查等服务，可用于自动下发随访问卷、复诊提醒、用药提醒等场景。
异常事件配置
在管理路径内支持按专病病种管理要求，配置监测指标的异常阈值，并设置异常等级。超过阈值后，会进行异常自动上报。同时支持异常的预处理方案配置，可通过文字、表单、链接等方式让患者得到实时的异常问题解决方案。
管护计划维护
支持专病管护计划的维护，包括查看计划进度、计划内异常上报、计划内管理患者情况，支持关键数据指标的统计和分析。
患者中心
纳管患者管理
支持对患者的档案、基本信息、重点指标、病史、住院记录、门诊记录、体检记录、检查报告、检验报告等信息查看，并可通过列表展示。
患者日程管理
展现患者的量表、宣教、文本提醒等日程记录，含日程完成情况、完成时间；支持对日程进行手动增删补改；
患者入组审核
对患者自主提交的入组申请进行审核操作；支持按分组维护查看患者列表；可对患者进行移入、移出分组操作。
医护随访
医患在线沟通（1V1）
医护人员可以在管理后台对患者发起在线沟通，沟通通道包含私聊、群聊，内容包含图文聊天、发量表、发宣教等等；也支持向患者发送信息或拨打电话，可用于医护人员对重点患者或者发生异常患者进行主动随访和干预。
医患在线沟通（1Vn）
医护人员可以在后台对加入管理计划的患者进行团队管理。在群聊通道里团队成员均可以对患者进行管理。包含：回答患者咨询、为患者发送宣教、发送随访，评估量表等。
异常预警及处理
异常事件
查看异常事件详情，触发患者，触发原因等；根据患者名字、触发异常事件类型来筛选异常事件；处理异常事件时可关联查看患者档案，关联线上沟通功能。
AI健管助手
辅助模式
医护人员在回复患者咨询问题时，如果想要减少打字或者回复内容更加系统性，可以打开辅助模式，由AI生成回复，医护一键复制回复患者。
托管模式
医护人员同时面临大量患者咨询或突然有事离开电脑，可以将回复的工作托管给AI来代替回答。AI回复的内容会经过医疗，医务的进一步合规审查，如果审查未通过，会自动拦截，回复内容审查通过后，会自动发送给患者。
专病随访知识库
健康宣教
支持按科室、类型、作者等维度进行健康宣教文章的创建和管理，并支持健康宣教的数据统计和分析。宣教内容包括图文、视频等形式，可用于疾病知识科普、生活方式指导、疾病康复指导等场景。
问卷表单
支持问卷的创建和维护，采用可视化的问卷编辑样式，问卷内选项包括单选、多选、文本填写、数字填写、逻辑跳转等，可用于满意度调查、诊后病情反馈、用药效果反馈等多种场景。
评估量表
支持问卷的创建和维护，评估表单选项包括单选、多选、文本填写、数字填写、逻辑跳转等，可计算表单得分，用于健康风险评估、疾病风险等级评估、术后居家康复情况评估等多种场景。
平台配置
组织管理
创建医院、院区、科室、医护、团队信息，支持编辑、启用停用操作。
系统设置
账户创建、编辑、停用启用、密码重置、关联角色；角色维护及对应菜单权限和数据权限配置。
系统日志
对于在线沟通、计划创建及更改等操作记录留痕，确保系统操作可查看可追溯。
运营配置
客服配置
支持根据医院或者管理计划配置客服，支持拨打电话和长按微信二维码联系客服。
用户协议配置
支持按照医院或者管理计划单独配置患者入组的患者须知同意书。
全院智能随访管理平台
全院随访管理
平台支持全院门诊、住院、手术、满意度等随访，支持专科随访；
支持随访自动入组；支持患者列表查看、随访登记、常规随访和电话随访，支持随访任务审核；
常规随访：查看当前医护医院随访组关联的所有随访计划，以及计划中的待执行任务、执行中任务、已过期任务、已完成任务。支持对已过期任务发起电话随访。专科随访支持手动导入患者。
电话随访：支持两类患者的电话随访，一类是随访方式选择了电话的患者，一类是已过期的患者。支持电话完成后填写如本次随访结果并提交。支持电话随访中，根据接口对接情况，提供查看当前患者的健康档案信息，如门诊记录、住院记录、随访记录、检查检验记录等。支持电话随访时查看预先配置好的知识库
随访登记：支持通过接口自动获取出院病人列表，基本信息以及住院期间的临床数据，患者数据获取到后会自动展示在患者列表中。
患者列表：支持查看随访系统中的患者列表。支持在患者详情页，查看每个患者的所有随访计划和随访任务，以及根据接口对接情况，提供查看当前患者的健康档案信息，如门诊记录、住院记录、随访记录、检查检验记录等。
送审单审核：支持查看送审单审核列表。支持医生对将要执行的检查单任务和检验单任务进行审核，审核通过才会发送给患者，审核不通过，任务直接终止。
随访规则
需支持配置全院执行、专科专病执行的随访规则，支持按病种来配置不同的随访规则；
支持规则关联科室和疾病，支持设置入组规则，单次执行随访路径配置和周期性执行随访路径配置；
支持规则类型根据业务场景联动和接口对接情况，提供关联健康宣教、满意度调查、服药提醒、预约挂号、检查检验下单、图文/视频问诊功能；
支持随访计划管理，支持随访计划与随访规则绑定；支持根据入院/出院/门诊患者的诊断自动关联随访规则，并根据里面的随访路径自动生成随访任务；
全院随访规则：
支持配置全院执行的随访规则。
支持按病种来配置不同的随访规则。
支持规则关联科室和疾病。
支持设置入组规则，规则可以根据入院记录单/出院记录单的字段内容来配置。
支持规则类型根据接口对接情况，提供关联健康宣教、满意度调查、服药提醒功能。
随访计划
支持按科室新增随访计划，配置随访计划的开始日期和结束日期。
支持随访计划绑定随访规则。
支持根据入院/出院/门诊患者的诊断自动关联随访规则，并根据里面的随访路径自动生成随访任务。
随访统计
需支持按医院、随访科室、随访模板、随访日期及随访样本信息进行随访统计搜索查询；支持统计结果查看、导出功能；支持统计结果表格及图像化直观展示；支持随访样本统计、随访占比统计、人员工作量统计、年度工作量统计、随访组工作量统计等统计方式；
宣教中心
需支持健康宣教、满意度问卷、健康问卷等内容的新增、编辑和删除功能。
支持手动给患者发送健康宣教、调查问卷；支持按照患者的类型、诊断等字段筛选患者；支持查看发送的宣教记录、问卷记录；
随访组管理
需支持对随访组和随访成员的新增创建、修改、删除；支持按所属医院、随访科室、关键字进行查询；
知识库管理
需支持对知识库和知识库标签进行新增、修改、删除；支持批量导入知识库信息；支持按知识库关键字进行查询；
智能引擎建设
通过专属定制化自动健康管理服务，管理患者首诊、入院、出院、复诊、复查、再次入院的整个康复治疗流程，健康管理服务通过随访规则引擎，根据设置好的随访规则，自动生成任务并自动执行，提高患者管理的工作效率减少工作量，增强患者的就医获得感觉。
宣教中心业务联动
智能引擎需主动推送入院、特检、术前术后、出院等宣教内容，疾病健康宣教内容，以及满意度问卷提醒给患者，患者打开推送链接可以直接进行便捷阅读。
资源中心业务联动
智能引擎可根据配置的规则，识别到随访患者有体检复检预约、随访复检预约、线上问诊检查检验预约需求时，推送检查检验预约提醒给患者，患者打开推送链接可以直接打开检查检验的预约界面直接预约线下到院检查时间。
图文咨询业务联动
居民在体检后、上门护理服务过程中、居家监测指标异常时，需根据配置好的规则推送图文咨询提醒给患者，患者打开推送链接可以直接打开图文咨询申请界面申请进行图文咨询。
视频问诊业务联动	
居民在体检后、上门护理服务过程中、居家监测指标异常时，需根据配置好的规则推送图文咨询提醒给患者，患者打开推送链接可以直接打开视频问诊申请界面进行视频问诊申请。
慢病续方业务联动
当随访患者有慢病续方需求时，智能引擎会根据配置好的规则推送慢病须发提醒给患者，患者打开推送链接可以直接打开慢病续方申请界面。申请并审核完成后，药品可直接快递寄送到患者家门口，做到患者就医“一次都不跑”。
护理上门业务管理
业务管理：
护理订单管理
支持管理员后台查看全部订单情况，包括下单信息、支付信息、接单信息、签到签退记录、护理记录、患者自评、医疗垃圾处理记录等；支持后台发起退款（若院内提供退款接口，则可实现自动退款）。
护理服务管理
支持管理员对订单进行指派服务人员、重新指派服务人员。
机构项目信息管理
支持管理员维护机构信息，并配置护理服务项目基础信息、上架下架情况。
模板配置管理
支持管理员后台配置患者评估模板、护理记录模板，并关联服务项目。
护士排班管理
支持管理员后台对护士进行排班。
护士评分管理
支持管理员查看护士评分。
服务监控
提供运行日志、服务监管，并支持实时接收护士报警响应；如紧急情况下护士发起报警，后台安全员将实时接收到地图定位以及订单详情的报警信息，以便即时干预；追溯护理人员的起点、终点位置，出门时间、签到时间、结束服务时间、护理记录、垃圾处理记录。
任务调度
支持根据患者需求合理调度护士资源。
培训管理
支持培训活动发布，维护培训活动上下架，审核培训报名人员，并在线授予证书。
平台管理：
区域机构管理
支持区域内多机构、多角色账号的统筹管理，并支持根据角色分配业务权限。
后台账号管理
支持新增后台管理员账号信息，更改管理员密码；支持新增后台管理员账号。
业务信息管理
支持科室、医护人员、服务项目的基础信息维护。
后台账号权限管理
支持配置后台管理人员的访问权限。
黑名单管理
支持对用户进行黑名单管理，包括移入移出黑名单列表。
系统设置：
弹窗管理
支持管理员发布、修改、新增开屏弹窗内容。
系统设置
包含服务超时费设置、路程费设置、抢单失效时长设置、收入分成设置等。
业务设置
包含banner设置、护士端公告设置等。
互联网医院运营服务
运营策略
核心用户画像
成都中医药大学附属医院（四川省中医医院）互联网医院的核心用户群体可以根据年龄、需求和地域特点划分为以下三类：
人群类型
人群画像
年轻人群（20-40岁）
核心特征：注重健康管理、热衷于获取医学科普知识，对便捷性、互动性和趣味性有较高要求。
主要需求：通过长视频、直播、图文等形式了解健康常识、疾病预防方法以及日常药食同源的养生技巧；关注个性化健康管理方案，如减肥、睡眠调理等。
行为习惯：活跃于抖音、微信视频号等社交媒体平台，倾向于参与互动性、专业性强的内容。
老年群体（50岁以上）
核心特征：以慢性病管理为主，对在线问诊的便利性和专业性有较高依赖，同时可能存在一定的手机操作障碍。
主要需求：需要针对性强的健康指导，如糖尿病、高血压、关节炎等慢性病的日常护理建议；
行为习惯：更倾向于通过语音播报、短视频获取信息；偏好稳定的1对1互动和服务支持，如一对一咨询、定期健康提醒等。
外地患者（跨区域就医者）
核心特征：由于地理位置限制，无法频繁到院就诊，但对优质医疗资源有强烈需求。
主要需求：远程诊疗服务、药品配送支持以及针对特定疾病的权威治疗方案解读。
行为习惯：关注名医案例分享、线上问诊指引、线上购药指引等实际问题；对优惠活动敏感，愿意尝试高效、便捷的线上医疗服务。
差异化运营策略
针对上述三类核心用户群体的不同特点，制定差异化的运营策略，以满足其个性化需求并提升用户黏性。
人群
内容形式
运营手段
年轻人群
长视频讲解：邀请资深医生录制详细讲解肥胖或睡眠调理的视频，内容通俗易懂且实用性强。
图文内容：结合热点制作趣味性的中医科普长图文内容。
趣味问答：设计互动性强的内容，如“你的体质适合喝哪种茶？”测试题。
在抖音平台发布高传播性的视频内容，结合热门话题和标签吸引流量；
在微信视频号开展深度讲座或直播答疑，为用户提供更多专业价值；
定期推送个性化健康资讯，增强用户对平台的信任感。
老年群体
短视频科普：通过通俗易懂的方式讲解健康知识，例如“每天3步帮助控制血压”。
视频教程：制作清晰的操作指南，帮助老年人掌握如何使用互联网医院的功能。
语音播报：推出每日健康提醒音频，方便老年人收听学习。
建立专属微信群或QQ群，为老年用户提供一对一咨询服务，解答常见问题；
定期发送健康提醒短信或语音消息，例如“今日血压监测小贴士”；
开展线下义诊与线上问诊相结合的活动，降低老年人的使用门槛。
外地患者
名医案例分享：通过真实案例展示重点科室和优势病种的治疗效果，增强患者信心。
线上诊疗指南：详细介绍线上问诊的流程、注意事项及常见问题解答。
康复指导课程：针对术后康复、慢性病管理等需求，提供系统化的视频课程。
加强对外地患者的线上购药服务宣传，如外地用户的消息提醒；
设置专属优惠活动，例如首次问诊减免部分费用、药品配送免运费等；
定期举办名医线上直播，为外地患者提供与专家直接沟通的机会。
IP打造运营
依托成都中医药大学附属医院（四川省中医医院）互联网医院门户平台优质的医疗资源，通过系统化的内容策略和多层次的IP打造，有效提升品牌形象、增强用户粘性，并实现流量转化。以下是具体内容规划：
医生IP打造
潜力医生筛选
从重点科室中挑选具备专业能力、表达能力和个人魅力的医生作为IP打造对象。优先选择以下科室：
中医妇科：聚焦月经不调、不孕症等女性健康问题；
内分泌科：专注糖尿病、甲状腺疾病等慢性病管理；
眼科：主打眼干燥症、近视防控等现代高发问题；
肛肠科：覆盖痔疮、便秘等常见病症。
（等其他科室）。
筛选标准包括：
具备扎实的专业背景和丰富的临床经验；
表达能力强，能够用通俗易懂的语言讲解复杂医学知识；
对社交媒体有一定了解，愿意参与内容创作与互动。
个人品牌定位
需帮助每位医生建立各自的细分领域人设和定位，突出专业特色和个人风格。例如：
“中医妇科专家”：专注于女性生殖健康与调理；
“糖尿病管理达人”：提供科学控糖方法与日常护理建议；
“近视防控先锋”：关注青少年视力健康与预防措施；
“痔疮治疗能手”：分享无痛手术与术后康复指导。
内容形式
根据不同平台的特点，设计多样化的内容形式：
抖音
微信视频号
短视频科普：针对生活中常见问题的简短科普，如“5个简单动作缓解颈椎痛”；
案例分享：展示真实患者的诊疗过程与效果；
门诊实录：记录医生与患者的真实互动场景，增加亲和力。
深度讲座：针对某一专题进行多场详细讲解，如“糖尿病患者的饮食指南”；
直播答疑：定期开展直播活动，解答用户提问并提供个性化建议。
品牌宣传：医院活动或中医药文化相关的宣传视频内容。
培训与激励机制
为医生提供全方位支持，确保内容的高质量输出：
拍摄培训：定期为医生培训短视频拍摄技巧及镜头前表现力提升；
奖励机制：设立流量扶持计划（如推荐至平台首页）、荣誉激励（如年度最佳医生IP奖）。
病种IP打造
重点病种选择
根据成都中医药大学附属医院（四川省中医医院）的优势科室和服务特点，建议围绕以下重点病种，进行IP打造：
中医妇科：月经不调、不孕症；
中医内分泌科：糖尿病、甲状腺疾病；
中医眼科：眼干燥症、近视防控；
中医肛肠科：痔疮、便秘。
内容策划
围绕每个病种，构建完整的内容体系，涵盖病因解析、治疗方案解读、康复指导等环节：
病因解析：以图文或短视频形式介绍疾病的成因与危害，帮助用户树立正确的认知；
治疗方案解读：结合实际案例，展示中医特色疗法（如针灸、推拿、中药调理）的效果；
康复指导：制作康复训练教程或健康管理手册，引导用户进行自我护理。
线上线下结合
线上：推出专题科普栏目，组织名医直播讲座，线上义诊，吸引用户关注；
线下：举办义诊活动、健康讲座等，为患者提供面对面交流机会，同时推广线上服务。
医院IP打造
品牌形象塑造
强调成都中医药大学附属医院（四川省中医医院）的独特优势，包括百年传承的中医药文化、先进的诊疗技术和贴心的服务理念。通过统一的视觉识别系统（如LOGO、标语）和语言风格，强化品牌记忆点。
宣传素材制作
制作高质量的宣传内容，展现医院的专业实力和特色中医药服务：
纪录片：讲述医院发展历程、名医故事及成功案例；
宣传片：聚焦医院特色科室与服务项目，突出“中医+科技”的创新模式；
患者故事：采访典型患者，分享他们的就医体验与康复历程。
公益活动
常态化组织各类专题免费义诊、健康讲座等活动，进一步扩大社会影响力：
免费义诊：定期在社区、学校、企业等地开展义诊服务，普及健康知识；
健康讲座：邀请名医走进基层，向公众传授养生技巧与疾病预防方法；
公益捐赠：联合慈善机构，为贫困患者提供医疗援助，彰显医院的社会担当。
区域特色中医药IP打造
品牌形象塑造
充分利用四川地区丰富的中医药资源，打造具有地域特色的中医药文化IP。例如：推广川派中医经典方剂，如桂枝汤；展示四川道地药材（如川贝母、黄连）的独特价值。
文化传播
结合中国传统节日，推出专题内容，弘扬中医药文化，同时带动成都中医药大学附属医院（四川省中医医院）特色中医药的销售，例如：
春节：发布“春节养生秘籍”，指导合理饮食与避免节日综合征，推广成都中医药大学的调理脾胃、帮助消化的中药产品。
端午节：讲解艾草的作用与使用方法，并介绍传统香囊制作，带动含有艾草成分的保健产品或香囊的销售。
中秋节：分享秋季润肺养阴食谱，推出特制的润肺茶饮或滋补品，弘扬根据季节变化调整饮食以养生的理念。
清明节：提供“清明时节养生指南”，讲述春季养肝的重要性，并带动医院的护肝保健品或草药茶。
夏至：推出“夏至养生之道”，强调夏季防暑降温的方法，宣传推出清凉解暑汤剂或饮品。
立秋：分享“立秋调整体质方案”，建议通过饮食调整适应气候变化，推荐医院专门配制的秋季养生汤包或药材。
重阳节：宣传老年人健康保养知识，包括运动方式和饮食调养，推广适合老年人保健食品或补品。
冬至：提供“冬至进补全攻略”，介绍温补食品和保暖保健方法，推广医院研发的温补类中药制品或足浴包。
小寒/大寒：分享“寒冬保暖与养生技巧”，包括衣物选择和暖身食物，促进医院具有暖身效果的保健品或外用膏药的销售。
本地合作
与本地企业、媒体平台、自媒体KOL建立合作关系，共同推动川派中医文化的传播：
企业合作：与药企联合开发满足多样化需求的中医药产品，如养生茶饮、保健食品；
媒体合作：邀请地方媒体平台宣传转发医院的各类内容，扩大曝光率；
KOL联动：与知名健康博主合作，通过短视频、文章等形式推广中医药知识。
通过以上内容策略与IP打造，成都中医药大学附属医院（四川省中医医院）互联网医院门户平台将逐步形成以医生IP为核心、病种IP为重点、医院IP为基础、区域特色IP为亮点的品牌矩阵，进一步巩固在行业内的品牌影响力，同时为用户提供更加丰富、专业和个性化的医疗服务。
线上流量运营
线上运营是成都中医药大学附属医院（四川省中医医院）互联网医院门户平台实现流量增长和品牌传播的核心驱动力。通过抖音和微信视频号两大体系的精准布局，结合跨平台整合营销策略，可以最大化提升用户触达率与转化效果。以下是具体运营规划：
抖音运营体系
内容规划
抖音作为短视频领域的头部平台，其算法推荐机制和庞大的用户群体为医疗科普提供了巨大的传播潜力。针对这一特点，制定以下内容方向：
日常科普：以轻松趣味的方式传递健康知识，例如“3个动作缓解肩颈酸痛”“为什么熬夜会伤肝？”等；
趣味问答：设计互动性强的问题，如“你的体质适合喝哪种茶？”并通过评论区引导用户参与讨论；
真实案例分享：展示患者从初诊到康复的全过程，突出中医特色疗法的效果；
门诊实录：记录医生与患者的诊疗对话，展现专业性与亲和力。
账号矩阵
建立主账号与子账号协同运营的账号矩阵，覆盖不同科室与病种需求：
主账号：成都中医药大学附属医院（四川省中医医院）官方账号，发布综合性内容（如医院动态、公益活动、名医访谈）；
科室子账号：分别为中医妇科、内分泌科、眼科、肛肠科开设独立账号，聚焦细分领域内容（如“糖尿病管理达人”“青少年近视防控先锋”）。
各子账号之间形成联动效应，例如主账号定期转发子账号优质内容，扩大影响力。
数据驱动优化
通过数据分析不断优化内容方向与运营策略：
定期监控点赞、评论、转发等核心指标，识别高热度内容类型；
根据用户活跃时间调整发布时间，确保内容获得最大曝光；
借助抖音后台工具（如创作者中心）分析用户画像，精准定位目标人群。
微信视频号运营
内容方向
微信视频号的用户群体更注重深度内容与长期价值，因此内容应偏重专业性和实用性：
慢性病管理系列课程：推出系统化的健康管理课程，如“糖尿病患者的饮食指南”“睡眠障碍调理”等；
深度讲座：邀请名医围绕某一专题进行详细讲解，吸引对专业知识感兴趣的用户；
直播答疑：定期开展直播答疑类型的活动，解答用户提问并提供个性化建议。
公众号联动
利用微信生态的优势，将视频号内容与公众号文章相结合，扩大覆盖面：
在公众号推送中嵌入视频号链接，形成公众号文章与视频号内容的联动；
发布长文扩展视频内容，如“视频中提到的5个养生小技巧，详细解读来了！”；
设置互动话题，鼓励用户在评论区留言或参与投票，增强粘性。
社群运营
建立基于微信生态的垂直社群，为用户提供持续服务：
按病种或兴趣划分社群（如“糖尿病管理群”“养生爱好者群”），便于精准推送相关内容；
定期发送健康提醒、科普资讯及优惠活动信息，保持用户活跃度；
邀请医生定期进入社群答疑，增强用户信任感。
跨平台整合营销
为了实现资源的最大化利用，可适当整合抖音与微信视频号的内容，并借助其他辅助渠道扩大影响力：
内容互通：将抖音上的爆款短视频同步至微信视频号，并根据平台特点进行适当调整；同时将微信视频号中的深度内容精简后发布至抖音；
微博与小红书推广：在微博上发布热点话题（如“#中医养生小妙招”），吸引用户关注；在小红书上分享真实的患者故事或医生推荐的健康产品，激发用户共鸣；
KOL合作：与健康领域的知名博主、网红合作，通过联合创作内容或直播带货的形式，进一步扩大传播范围。
节日营销与活动策划
结合重要时间节点策划主题活动，提升用户参与度，例如：
春节：推出“春节养生秘籍”系列内容，如如何合理饮食、避免节日综合征；
国际妇女节：可以开展针对女性健康的专项义诊活动，如妇科疾病咨询、乳腺健康检查等，提供免费的线上咨询服务或特定项目的优惠。
全国爱眼日：组织眼科相关的义诊活动，特别是针对青少年近视、老年人白内障等问题，提供专业的眼科医生在线咨询。
糖尿病日：策划中医糖尿病调理的专题系列内容，组织内分泌科专家进行糖尿病管理和预防的专题义诊，帮助患者了解如何通过饮食、运动等方式控制血糖水平。
世界睡眠日 ：开展“改善失眠”主题直播，邀请专家讲解中医调理方法；
双十一 ：推出线上购药优惠活动，吸引更多用户尝试线上服务。
通过以上线上运营策略，成都中医药大学附属医院（四川省中医医院）互联网医院门户平台将充分利用抖音和微信视频号的传播优势，结合打造全方位、多层次的内容生态。提升平台流量和用户活跃度，进一步强化品牌影响力，为后续的线上线下联动奠定坚实基础。
线下活动与场景融合
线下活动是互联网医院门户平台实现线上线下联动的重要抓手，通过举办主题义诊、健康讲座和名医面对面等活动，不仅可以增强用户对平台的信任感，还能有效引导线下用户向线上转化。同时，通过线上线下一体化的构建，进一步提升用户体验和服务效率。以下是具体的线下活动规划与场景融合策略：
线下推广活动
主题义诊
围绕重点科室和优势病种，定期开展免费义诊活动，吸引目标人群参与：
活动形式：邀请院内名医团队现场坐诊，提供初步诊断、健康咨询和治疗建议；
覆盖范围：院内或医院周边社区，扩大活动影响力；
附加服务：活动现场设置扫码关注环节，引导用户注册互联网医院账号并领取优惠券（如首次问诊减免或药品配送折扣）。
健康讲座
组织专题健康讲座，邀请名医分享专业知识，增强用户对疾病预防和健康管理的认知：
内容方向：结合热点话题设计讲座主题，例如“秋冬季节如何养肺护肺”“青少年近视防控指南”等；
互动环节：设置问答时间，让观众与医生直接交流；
宣传渠道：通过抖音、微信视频号提前预热，并直播部分讲座内容，吸引更多线上用户观看。
名医面对面
打造患者与名医近距离交流的机会，强化医生IP形象：
活动形式：针对特定疾病组织小型沙龙或座谈会，每场限定人数（如30人），确保每位参与者都能获得充分沟通机会；
内容亮点：医生分享诊疗经验、成功案例及日常生活中的养生技巧；
后续跟进：活动结束后，将相关内容剪辑成短视频发布至抖音和微信视频号，持续扩大影响力。
线上线下一体化构建
为了实现线上线下服务的无缝衔接，需从引流、转化到复购全流程优化用户体验，形成完整的医疗服务生态闭环。
线下引流至线上
扫码关注：在线下活动场地设置二维码展板，引导用户扫码关注互联网医院官方账号；
优惠激励：提供专属福利（如免费体验券、药品折扣券），鼓励用户完成注册并尝试线上服务；
社群运营：活动现场建立微信群，方便后续推送科普资讯和优惠信息。
线上导流至线下
便捷就医：通过互联网医院平台的“线上加号、线上开单”功能，为用户提供便捷的就医服务；
名医推荐：在线上科普内容中嵌入名医线下出诊信息，吸引用户前往医院就诊；
康复指导：针对术后或慢性病患者，推荐线下康复训练课程或健康沙龙活动。
数据驱动的闭环优化
用户行为追踪：记录用户从线下到线上的转化路径，分析各环节的流失率与转化率；
精准推送：根据用户需求标签（如病种、年龄、地域），推送个性化的服务提醒或优惠信息；
服务反馈收集：通过问卷调查或电话
安全体系设计方案
建设目标
此次主要依据《信息安全技术网络安全等级保护基本要求》（GB/T22239-2019）（以下简称《基本要求》）和《信息安全技术网络安全等级保护安全设计技术要求》（GB/T25070-2019）等相关政策标准，针对成都中医药大学附属医院信息系统的安全等级保护提出设计方案。
设计方案从信息安全等级保护技术体系、安全等级保护管理体系两个方面提出安全建设的整体框架，建立“一个基础”（安全物理环境）、“一个中心”（安全管理中心）保障下的“三重防护体系”（安全通信网络、安全区域边界、安全计算环境）的架构，使得成都中医药大学附属医院信息系统具备等保三级防护能力，由于医院前期各大核心业务系统已通过等级保护测评，本期项目中尽量利旧原有安全能力，只针对部分老旧产品进行替换更新处理。
设计依据
本方案严格参考的国家网络安全政策法规、等级保护安全标准以及卫生行业相关规范进行设计。
政策法规
《中华人民共和国网络安全法》
《中华人民共和国计算机信息系统安全保护条例》（国务院147号令）
《关于加强信息安全保障工作的意见》（中办发[2003]27号）
《关于信息安全等级保护工作的实施意见》（公通字[2004]66号）
《信息安全等级保护管理办法》（公通字［2007］43号 ）
《关于开展信息安全等级保护安全建设整改工作的指导意见》（公信安〔2009〕1429号）
《卫生行业信息安全等级保护工作的指导意见》卫办发〔2011〕85号
《“十四五”全民健康信息化规划》
《医疗卫生机构网络安全管理办法》
安全标准：
GB/T 22239-2019 《信息安全技术 网络安全等级保护基本要求》
GB/T 22240-2020 《信息安全技术 网络安全等级保护定级指南》
GB/T 25070-2019 《信息安全技术 网络安全等级保护安全设计技术要求》
GB/T 28448-2019 《信息安全技术 网络安全等级保护测评要求》
GB/T 28449-2018 《信息安全技术 网络安全等级保护测评过程指南》
GB/T 36627-2018 《信息安全技术 网络安全等级保护测试评估技术指南》
GB/T 25058-2019 《信息安全技术 信息系统安全等级保护实施指南》
GB 17859-1999 《计算机信息系统安全保护等级划分准则》
现状及风险需求分析
安全技术体系需求分析
通信网络安全需求
根据对通信网络安全进行风险分析，成都中医药大学附属医院信息系统安全等级保护建设通信网络安全方面的需求：
序号
控制项
需求
1
网络架构
所有安全设备应按照实际业务峰值要求进行性能配备，并且提供双机热备和多链路冗余设计；需要对整个信息系统网络进行安全区域的划分，并进行IP和VLAN的规划，在不同的安全区域边界需要提供相应的安全设备和技术进行隔离
2
通信传输
需要对信息系统网络内部以及外联的通信进行传输校验和加密的控制保证数据传输的完整性和保密性
3
云计算网络架构
需要部署相应的安全设备和技术，对不同云主机虚拟网络之间进行隔离；并且对云服务客户业务提供通信传输、边界防护、入侵防范等安全防护
区域边界安全需求
根据对区域边界进行风险分析，成都中医药大学附属医院信息系统安全等级保护建设区域边界安全方面的需求：
序号
控制项
需求
1
边界防护
需要部署相应的安全产品和技术保证跨越边界访问的数据流只能通过受控的接口进行通信，并且能够对与外部和内部用户接入网络进行检查和权限控制，同时能够控制无线终端的接入
2
访问控制
需要在边界部署相应的安全产品，并且对跨越边界的访问进行控制，关闭所有接口，只允许通信接口通过；应清理无效的访问控制策略，优化访问控制列表，应能够根据源地址、目的地址、源端口、目的端口、协议号、会话状态信息、应用协议、应用内容进行访问控制
3
入侵防范
需要在关键网络节点处部署相应的安全产品和技术，对从外部和内部发起的网络攻击进行检测和限制，对网络行为进行分析，特别是新型网络攻击行为的分析，当检测到攻击行为时，能够记录攻击源IP、攻击类型、攻击目标、攻击时间，并能提供报警
4
安全审计
需要在关键网络节点处部署相应的安全产品和技术，对每个用户的行为和重要安全事件进行审计，审计记录应包括时间和日期、用户、事件类型、事件是否成功等信息，应对审计记录进行保护和备份避免未预期的修改、删除或覆盖等；应对远程访问的用户行为、访问互联网的用户行为进行单独的审计和数据分析
5
云计算访问控制
需要在虚拟化网络边界部署相应的安全产品和技术，对跨越边界访问的数据流进行访问控制
6
云计算入侵防范
需要在云计算平台内部部署相应的安全产品和技术，对云服务客户发起的网络攻击行为进行检测，并能记录攻击类型、攻击时间、攻击流量等；对虚拟网络节点的网络攻击行为，并能记录攻击类型、攻击时间、攻击流量等；应能对虚拟机与宿主机、虚拟机与虚拟机之间的异常流量进行检测，在检测到网络攻击行为、异常流量情况时进行告警
计算环境安全需求
根据对计算环境进行风险分析，成都中医药大学附属医院信息系统安全等级保护建设计算环境安全方面的需求：
序号
控制项
需求
1
访问控制
需要部署相应的安全产品和技术，对登录的用户分配账户和权限；应重命名或删除默认账户，修改默认账户的默认口令；还要及时删除或停用多余的、过期的账户，避免共享账户的存在；应授予管理用户所需的最小权限，实现管理用户的权限分离；应由授权主体配置访问控制策略，访问控制策略规定主体对客体的访问规则；访问控制的粒度应达到主体为用户级或进程级，客体为文件、数据库表级；应对重要主体和客体设置安全标记，并控制主体对有安全标记信息资源的访问
2
安全审计
需要部署相应的安全产品和技术，对每个用户的行为和重要安全事件进行审计；审计记录应包括事件的日期和时间、用户、事件类型、事件是否成功及其他与审计相关的信息；应对审计记录进行保护，定期备份，避免受到未预期的删除、修改或覆盖等；应对审计进程进行保护，防止未经授权的中断
3
入侵防范
部署相应的安全产品和技术，对终端和主机进行检测应遵循最小安装的原则，仅安装需要的组件和应用程序；应关闭不需要的系统服务、默认共享和高危端口；应通过设定终端接入方式或网络地址范围对通过网络进行管理的管理终端进行限制；应提供数据有效性检验功能，保证通过人机接口输入或通过通信接口输入的内容符合系统设定要求；应能发现可能存在的已知漏洞，并在经过充分测试评估后，及时修补漏洞；应能够检测到对重要节点进行入侵的行为，并在发生严重入侵事件时提供报警
4
恶意代码防范
部署相应的安全产品和技术，能够及时识别入侵和病毒行为，并将其有效阻断
5
数据完整性
部署相应的安全产品和校验技术、密码技术，对于传输过程中和存储过程中的数据进行校验保证数据的完整性，包括但不限于鉴别数据、重要业务数据、重要审计数据、重要配置数据、重要视频数据和重要个人信息等
6
数据保密性
部署相应的安全产品和密码技术，对于传输过程中和存储过程中的数据进行加密保证数据的保密性，包括但不限于鉴别数据、重要业务数据、重要审计数据、重要配置数据、重要视频数据和重要个人信息等
7
数据备份恢复
由云平台建设管理提供，此处不进行讨论
8
剩余信息保护
部署相应的安全产品和技术，保证鉴别信息所在的存储空间被释放或重新分配前得到完全清除；保证存有敏感数据的存储空间被释放或重新分配前得到完全清除
9
云计算身份鉴别
需要部署相应安全产品和技术，保证远程管理云计算平台中设备时，管理终端和云计算平台之间应建立双向身份验证机制
10
云计算访问控制
由云平台管理层提供，此处不进行讨论
管理中心安全需求
根据对管理中心进行风险分析，成都中医药大学附属医院信息系统安全等级保护建设管理中心安全方面的需求：
序号
控制项
需求
1
系统管理
部署相应的安全产品和技术，对系统管理员进行身份鉴别，只允许其通过特定的命令或操作界面进行系统管理操作，并对这些操作进行审计；应通过系统管理员对系统的资源和运行进行配置、控制和管理，包括用户身份、系统资源配置、系统加载和启动、系统运行的异常处理、数据和设备的备份与恢复等。
2
审计管理
部署相应的安全产品和技术，对审计管理员进行身份鉴别，只允许其通过特定的命令或操作界面进行安全审计操作，并对这些操作进行审计；应通过审计管理员对审计记录应进行分析，并根据分析结果进行处理，包括根据安全审计策略 对审计记录进行存储、管理和查询等
3
安全管理
部署相应的安全产品和技术，对安全管理员进行身份鉴别，只允许其通过特定的命令或操作界面进行安全管理操作，并对这些操作进行审计。
4
集中管控
应划分出特定的管理区域，对分布在网络中的安全设备或安全组件进行管控；应能够建立一条安全的信息传输路径，对网络中的安全设备或安全组件进行管理；部署相应的安全产品和技术对网络链路、安全设备、网络设备和服务器等的运行状况进行集中监测；对分散在各个设备上的审计数据进行收集汇总和集中分析，并保证审计记录的留存时间符合法律法规要求；对安全策略、恶意代码、补丁升级等安全相关事项进行集中管理；对网络中发生的各类安全事件进行识别、报警和分析
安全管理体系需求分析
本次成都中医药大学附属医院信息系统等级保护安全建设涉及到的信息系统属三级信息系统，根据等级保护管理原则，我们将以《基本要求》中三级安全防护能力为标准，提出成都中医药大学附属医院信息系统的安全管理需求。
在成都中医药大学附属医院信息系统的管理方面，需要采取以下安全措施，以保证成都中医药大学附属医院信息系统的管理安全：
相关业务部门需要从决策层、管理层、执行层，组织信息、安全、业务等相关部门的人员，共同组建安全运维和管理机构，确保相关部门相互协作，按照决策层确定的目标组织实施和落实管理。
相关业务部门需要落实具体的管理人员，并按照“最小授权”、“相互约束”等原则进行管理人员职责划分的权限分配，保证成都中医药大学附属医院信息系统对管理人员的权限控制安全。
需要按照等级保护相关标准和文件要求，制定既满足《基本要求》合规性，又符合成都中医药大学附属医院实际情况的，具有可实施性的安全管理制度和安全运维制度，并通过加强培训和加强监督管理等方式，确保安全制度能够得到执行。
安全管理体系主要包括以下几个方面：
安全管理制度。需要根据《基本要求》和信息系统的业务特点，建立一套完善的网站系统的安全管理制度。
安全管理机构。需要根据《基本要求》和本单位实际情况，建立信息安全管理机构。
人员安全管理。对现有人员安全管理体系按照《基本要求》中相应内容进行适当调整。
系统建设管理。在对成都中医药大学附属医院信息系统的安全整改建设过程中，需按照《基本要求》建立针对成都中医药大学附属医院信息系统的管理制度。
系统运维管理。需要根据成都中医药大学附属医院信息系统的具体情况，组织相关部门和厂商，组建运维管理团队和支撑保障团队，加强成都中医药大学附属医院信息系统的日常运维管理。
大模型安全需求分析
敏感数据违规上传
在大模型应用中，用户上传的敏感文件若未进行脱敏处理，可能会带来用户核心数据/个人敏感信息泄露风险；敏感数据如被不当利用也会给用户带来不必要的困扰和隐私压力。

训练数据未脱敏风险
训练数据中可能包含用户内部机密，这些未脱敏的机密数据若发生泄露或被不法分子获取，会给用户造成巨大的经济损失。
文本提示词注入风险
攻击者通过精心构造提示词，诱导大模型返回敏感数据或生成违反伦理准则的内容，例如歧视性表述、违法操作指导或具有社会危害性的建议。
总体设计
区域划分
根据成都中医药大学附属医院网络现状、业务特点及安全需求，对其网络区域划分进行如下设计，将成都中医药大学附属医院网络划分医院内网区域和医院互联网区域两大部分；其中，内网划分为内网外联区、安全管理区、内网数据中心等，互联网区域的划分与内网相似，划分为外网外联区、外网前置服务区。
内网外联区域：确保所有跨越网络边界的访问和所有流入、流出的数据均通过其受控接口进行通信、接受安全检查和处理。
外网外联区域：确保所有跨越网络边界的访问和所有流入、流出的数据均通过其受控接口进行通信、接受安全检查和处理。
安全管理区：对成都中医药大学附属医院信息系统中设计的各类安全设备进行统一管理。
内网数据中心：提供成都中医药大学附属医院主要业务系统的承载和访问，提供云计算资源服务。本次拟建设系统将与原有核心系统隔离部署。
外网数据中心：为成都中医药大学附属医院提供应用的前置服务器，作为前端信息采集的中转及服务在外网映射的载体区域。
总体安全部署
根据成都中医药大学附属医院信息系统总体安全架构的设计，以及安全区域的划分，总体安全部署逻辑示意图如下图所示：

外网外联区域：
在成都中医药大学附属医院外网外联区域串联部署2台下一代防火墙系统（更新原有旧设备），对防火墙配置基于端口的访问控制策略，并启用入侵防御和防病毒安全检测引擎。
在内网边界与互联网边界之间部署2台双向网闸（更新原有旧设备并增补一台实现业务主干链路冗余），以白名单机制提供数据库同步、文件同步以及协议访问功能，对内外网交互的信息进行落地、还原、扫描、过滤、防病毒、审计等一系列安全处理，有效防止黑客攻击、恶意代码和病毒渗入，同时防止内部机密信息的泄露。
外网前置DMZ区：
在成都中医药大学附属医院互联网前置区边界处串联部署1台下一代防火墙系统，对防火墙配置基于端口的访问控制策略，并启用入侵防御和防病毒安全检测引擎。新增一台web应用安全防火墙，针对智能门户前置对外web服务进行安全防护。
内网外联区：
在成都中医药大学附属医院内网外联区边界处串联部署2台下一代防火墙系统（更新原有旧设备），对防火墙配置基于端口的访问控制策略，并启用防病毒安全检测引擎。
内网数据中心：
在成都中医药大学附属医院数据中心区在成都中医药大学附属医院智能门户业务区串联部署2台下一代防火墙系统开启主备模式，对防火墙配置基于端口的访问控制策略，对核心业务区域进行边界隔离，并启用入侵防御和防病毒安全检测引擎。确保数据核心区域的网络安全边界清晰，安全防护日志准确，对安全事件的判别更加清晰；
安全管理区：
除利旧原有安全设备外，增加一套大模型脱敏罩系统，为访问侧、用户应用侧提供了更专业的数据脱敏防护，有效解决用户对外访问开放大模型的时候避免自身敏感数据泄露的安全问题。
安全技术体系设计
全物理环境
机房建设参照等级保护三级物理环境安全控制项的要求，结合《电子信息系统机房设计规范》（GB50174-2008）标准进行建设，主要涉及的范畴包括环境安全（防火、防水、防雷击等）、设备和介质的防盗窃防破坏等方面。具体包括：物理位置选择、物理访问控制、防盗窃和防破坏、防雷击、防火、防水和防潮、防静电、温湿度控制、电力供应和电磁防护等控制点。
由于用户现有核心业务系统已通过等级保护测评，本方案不作详细叙述。
安全通信网络
网络架构
安全域划分
为了实现成都中医药大学附属医院信息系统的等级化划分与保护，需要依据等级保护的相关原则规划与区分不同安全保障对象，并根据保障对象设定不同业务功能及安全级别的安全区域，以根据各区域的重要性进行分级的安全管理。
成都中医药大学附属医院信息系统承载医疗、办公、系统开发测试等多项业务，需根据各项业务的性质和特点，将信息系统分成若干业务子系统，分别为各业务子系统确定安全保护等级。信息系统是进行等级保护管理的最终对象，为体现重点保护重要网络安全，有效控制信息安全建设成本，优化信息安全资源配置的等级保护原则，在进行信息系统的划分时应考虑以下几个方面：
1.	相同的管理机构
信息系统内的各业务子系统在同一个管理机构的管理控制之下，可以保证遵循相同的安全管理策略；
2.	相似的业务类型
信息系统内的各业务子系统具有相同的业务类型，安全需求相近，可以保证遵循相同的安全策略；
3.	相同的物理位置或相似的运行环境
信息系统内的各业务子系统具有相同的物理位置或相似的运行环境意味着系统所面临的威胁相似，有利于采取统一的安全保护；
4.	相似安全控制措施
信息系统内的各业务子系统因面临相似的安全威胁，因此需采用相似的安全控制措施来保证业务子系统的安全。
本次智能门户业务采用与原有核心业务隔离部署的模式，采用不同的安全控制策略进行防护。
高可用网络架构
单线路、单设备的结构很容易发生单点故障导致业务中断，因此对于提供关键业务服务的信息系统，应用访问路径上的通信链路、网关设备和交换设备，应尽可能采用可靠的冗余备份机制，以最大化保障数据访问的可用性和业务的连续性。
建议在成都中医药大学附属医院网络中，除了互联网接入链路应采用多运营商链路互备、关键业务系统应采用多服务器互备外，对于成都中医药大学附属医院网络重要安全域链路及相关的网络路由交换设备、安全网关设备等均采用冗余热备的部署方式，以提升网络系统的整体容错能力，防止出现单点故障。同时，需要重新评估业务高峰期时的网络流量、并发用户和新建网络连接等性能数据，必要时增加网络带宽、设备处理性能，保证网络带宽、网络设备的业务处理能力满足业务高峰期需要。
全区域边界
边界防护
跨界流量安全通信
在成都中医药大学附属医院外网外联区域、外网DMZ前置服务区、内网外联区边界分别串联部署下一代防火墙系统，并启用入侵防御和防病毒安全检测引擎；在内网智能门户业务区边界分别部署2台下一代防火墙系统做主备。对防火墙配置基于端口的访问控制策略，确保所有跨越网络边界的访问和所有流入、流出的数据均通过其受控接口进行通信、接受安全检查和处理；
内外网安全隔离
在成都中医药大学附属医院内网与互联网之间更新2套双向网闸，以白名单机制提供数据库同步、文件同步以及协议访问功能，对内外网交互的信息进行落地、还原、扫描、过滤、防病毒、审计等一系列安全处理，有效防止黑客攻击、恶意代码和病毒渗入，同时防止内部机密信息的泄露。在保持内外网络有效隔离的基础上，实现了两网间安全的、受控的数据交换。
另外，还应关闭成都中医药大学附属医院网络中所有路由器、交换机与安全设备等相关设备的闲置端口，进一步降低非法终端入网的风险。
访问控制
在成都中医药大学附属医院在成都中医药大学附属医院外网外联区域、外网DMZ前置服务区、内网外联区边界分别串联部署下一代防火墙系统，在内网数据中心边界分别部署2台下一代防火墙系统做主备。成都中医药大学附属医院内网与成都中医药大学附属医院外网之间部署2台双向网闸。
网闸采用的是白名单机制，默认阻断所有数据，只需通过策略对需要交换的数据放行，并检测数据的安全性。下一代防火墙根据各网络或区域访问需求，配置基于源地址、目的地址、源端口、目的端口、协议、应用协议和应用的访问控制策略，还需要在访问控制策略之后配置一条禁止所有网络通讯的策略，实现白名单机制。并且需要防火墙具备冗余策略检测功能，能支持策略命中分析、策略冗余分析、策略冲突检查，并且可在WEB界面显示检测结果，比如：红色为冗余策略，绿色为冲突策略等。
恶意代码和垃圾邮件防范
防恶意代码技术保障关键网络节点安全
对所有网络或区域边界都需要防范恶意代码攻击，主要有两种方式：（1）部署专业的防病毒网关；（2）下一代防火墙开启防病毒功能模块。
在成都中医药大学附属医院所有部署的下一代防火墙上开启防病毒功能模块对进出的网络数据流进行病毒、恶意代码扫描和和过滤处理，并提供病毒代码库的自动或手动升级，彻底阻断外部网络的病毒、蠕虫、木马及各种恶意代码向网络内部传播。
安全审计
日志统一收集与分析
建议成都中医药大学附属医院网络内所有的终端、服务器、网络设备、安全设备、应用系统、数据库和中间件均开启自身日志记录功能，对成都中医药大学附属医院全网的用户行为和重要安全事件进行审计，由现有的日志审计类安全产品通过Syslog、SNMP Trap、NetFlow、Telnet/SSH、WMI、FTP/SFTP/SCP、JDBC、文件等采集方式，统一收集审计对象海量的日志数据，从不同角度进行安全信息的可视化分析，以统计报表形式向审计管理员展示
入侵防范
外部入侵攻击防御
建议在下一代防火墙上开启入侵防御功能模块，并提供入侵攻击特证库的自动或手动升级，实时发现和阻止从外部网络发起的网络攻击行为，阻止来自其它网络区域的攻击流量对重要区域造成影响。并记录攻击行为的攻击源IP、攻击类型、攻击目标、攻击时间等，向运维管理人员发送告警信息。
安全计算环境
身份鉴别
通过在利旧现有运维审计类安全产品对运维管理员的身份进行验证，并限制其权限，使用口令复杂度限制，配置自动操作系统和数据库的管理口令进行修改，保证操作系统和网络设备的安全。
除此之外，系统还应实现以下一系列的安全措施：
对登录用户进行身份标识和鉴别，且保证用户名的唯一性；
根据基本要求配置用户名/口令，必须具备一定的复杂度；口令必须具备采用3种以上字符、长度不少于8位并定期更换；
启用登陆失败处理功能，登陆失败后采取结束会话、限制非法登录次数和自动退出等措施；
远程管理时应启用SSH等管理方式，加密管理数据，防止被网络窃听；
双因素认证方式，采用数字证书+密码进行身份鉴别。
访问控制
针对个别应用系统的访问权限，使用下一代防火墙进行访问控制，制定严格的访问控制安全策略，根据策略控制用户对应用系统的访问，特别是文件操作、数据库访问等。
安全审计
日志统一收集与分析
建议在利用现有的日志审计类产品，系统通过Syslog、SNMP Trap、NetFlow、Telnet/SSH、WMI、FTP/SFTP/SCP、JDBC、文件等采集方式，统一收集审计对象海量的日志数据，从不同角度进行安全信息的可视化分析，以统计报表形式展示位运维管理人员。
入侵防范
在核心交换机处，通过现有的安全探针类设备，对内网核心交换机的访问及出口流量进行分析、处理并向安全管理中心提供探针分析结果。并且对异常流量、入侵行为、僵尸木马病毒的定位溯源提供有效数据分析依据。
恶意代码防范
建议通过终端安全管理类技术手段，为全网终端制定统一查杀、统一升级、定时开关机、消息提醒、策略管理等功能任务，并收集客户端安全信息，直观的将病毒趋势统计、终端信息、病毒类型排行、病毒排行、终端危险排行等统计等情况进行统一展示。
剩余信息保护
为保证存储在硬盘、内存或缓冲区中的信息不被非授权的访问，操作系统应对这些剩余信息加以保护。用户的鉴别信息、文件、目录等资源所在的存储空间，操作系统应将其完全清除之后，才能释放或重新分配给其他用户。
采取的措施包括：
取消操作系统、数据库系统和运维审计等系统的用户名、登录密码自动代填功能。采用数据擦除工具，确保身份鉴别信息和敏感业务数据所在的存储空间被释放或重新分配前得到完全清除。
安全管理中心
三员管理
安全管理中心应做到系统管理员、审计管理员和安全管理员的三权分立，通过在安全管理区部署堡垒机为各类管理员提供统一管理平台接口，按照各类管理员业务上分工的不同，合理地把其划分为不同的类别或者组，严格限制各类管理员对运维对象的访问权限。并对各类管理员进行双因素身份鉴别，只允许其通过运维审计的命令或操作界面进行管理操作，并对这些操作进行审计。三类管理员的主要职责如下：
系统管理员
主要负责系统的日常运行维护工作。包括网络设备、安全产品、服务器和用户终端、操作系统数据库、业务系统的安装、配置、升级、维护、运行管理；网络和系统的用户增加或删除；网络和系统的数据备份、运行日志审查和运行情况监控；应急条件下的安全恢复。
安全管理员
主要负责系统的日常安全保密管理工作。包括网络和系统用户权限的授予与撤销；用户操作行为的安全设计；安全设备管理；系统安全事件的审计、分析和处理；应急条件下的安全恢复。
审计管理员
主要负责对系统管理员和安全保密员的操作行为进行审计跟踪、分析和监督检查，及时发现违规行为，并定期汇报情况。
集中管控
本方案在对成都中医药大学附属医院整体网络规划时，划分出特定的管理区域即：安全管理区，对分布在网络中的安全设备或安全组件进行统一管控。
安全运维集中管理
统一管理系统用户身份，按照业务上分工的不同，合理地把相关人员划分为不同的类别或者组，以及不同的角色对模块的访问权限。对系统管理员进行严格的身份鉴别，只允许其通过堡垒机的命令或操作界面进行系统管理操作，并对这些操作进行审计。
整体网络集中管理
通过收集成都中医药大学附属医院网络内的资产、流量、日志、网站等相关的安全数据，经过存储、处理、分析后形成资产态势、风险态势、安全态势及告警，辅助成都中医药大学附属医院人工智能与信息化部了解整体成都中医药大学附属医院网络安全态势。及时掌握网络安全威胁、风险和隐患，及时监测漏洞、病毒木马、网络攻击情况，及时发现网络安全事件线索，及时预警通报重大网络安全威胁，及时处置安全事件，提升成都中医药大学附属医院的风险发现能力，加快风险解决速度。
日志集中管理
配置成都中医药大学附属医院网络内所有的终端、服务器、网络设备、安全设备、应用系统、数据库和中间件均开启自身日志记录功能，对成都中医药大学附属医院全网的用户行为和重要安全事件进行审计，由日志审计系统通过Syslog、SNMP Trap、NetFlow、Telnet/SSH、WMI、FTP/SFTP/SCP、JDBC、文件等采集方式，统一收集审计对象海量的日志数据，从不同角度进行安全信息的可视化分析，以统计报表形式向审计管理员展示。
病毒防护集中管理
通过统一的终端安全管理，确保全网具有一致的防病毒策略和最新的病毒查杀能力。
安全管理体系设计
因医院原核心业务系统均通过等级保护测评，此处安全管理体系设计作为持续优化建议，医院可根据自身情况进行调整。
安全管理制度
根据等级保护基本要求对管理制度建设的要求，对安全策略体系进行规划。体系包括确定系统信息安全愿景和使命的信息安全总体目标，约束和指导人员信息安全工作的规章制度、管理办法和工作流程，规范系统、网络和安全管理员进行安全操作的技术标准和规范，文档结构如下图所示：

信息安全方针是纲领性的安全策略主文档，阐述了安全策略的目的、适用范围、信息安全目标、信息安全的管理意图等，是信息安全各个方面所应遵守的原则方法和指导性策略。是安全方面工作的最高指导文件。
规章制度
《信息安全组织体系和职责》：规定系统安全组织机构的职责和工作。
《信息安全岗位人员管理办法》：加强内部人员安全管理，依据最小特权原则清晰划分岗位，在所有岗位职责中明确信息安全责任，要害工作岗位实现职责分离，关键事务双人临岗，重要岗位要有人员备份，定期进行人员的安全审查。
《信息安全工作人员安全管理办法》：工作人员在录用、调动、离职过程中的信息安全管理，提出对信息安全培训及教育、奖励和考核的要求。
《信息安全培训及教育管理办法》：系统各层面信息安全培训的要求和主要内容。
《信息安全第三方人员安全管理办法》：必须加强第三方访问和外包服务的安全控制，在风险评估的基础上制定安全控制措施，并与第三方系统和外包服务系统签署安全责任协议，明确其安全责任。
《安全检查及考核管理办法》建立安全检查制度和安全处罚制度，对违反规章制度的处室和人员按照规定进行处罚。
《信息安全体系管理办法》：建设完整安全体系，实现从设计、实施、修改和维护生命周期的安全体系自身保障。
《信息安全策略管理办法》：安全策略本身应规范从创建、执行、修改、到更新、废止等整个生命周期的维护保障。
《信息安全安全现状评估管理办法》：信息安全体系的建设和维护，要通过及时获知和评价信息安全的现状，通过对于安全现状的评估，实施信息安全建设工作，减少和降低信息安全风险，提高信息安全保障水平。
《信息安全信息资产管理办法》：必须加强信息资产管理，建立和维护信息资产清单，维护最新的网络拓扑图，建立信息资产责任制，对信息资产进行分类管理和贴标签。
《信息安全IT设备弱点评估及加固管理办法》：增强主机系统和网络设备的安全配置，应定期进行安全评估和安全加固。
《信息安全预警管理办法》：对安全威胁提前预警，及时将国内外安全信息通知系统各级信息安全管理人员及工作人员，确保能够及时采取应对措施，以此降低系统的信息安全风险。
《信息安全安全审计及监控管理办法》：应部署网络层面和系统层面的访问控制、安全审计以及安全监控技术措施，保障业务系统的安全运行。
《信息安全项目立项安全管理办法》：加强项目建设的安全管理，配套安全系统必须与业务系统“同步规划、同步建设、同步运行”，加强安全规划、安全评估和论证的管理。
《安全运行维护管理办法》：建立日常维护操作规程和变更控制规程，规范日常运行维护操作。
《信息安全配置变更管理办法》：严格控制和审批任何变更行为。
《信息安全病毒防护管理办法》：加强系统病毒防治工作，提升成都中医药大学附属医院信息系统病毒整体防护能力，降低并防范病毒对于成都中医药大学附属医院信息系统业务造成的影响。
《信息安全补丁管理办法》：按照补丁跟进和发布、补丁获取、补丁测试、补丁加载、补丁验证、补丁归档这一流程进行补丁安全管理。
《信息安全账号口令及权限管理办法》：加强用户账号和权限管理，按照最小特权原则为用户分配权限，避免出现共用账号的情况。
《信息安全应急响应管理办法》：制定各业务系统的应急方案，及时发现、报告、处理和记录。
管理流程
包括：安全管理规定中与管理流程配合使用，管理规定中会提出涉及流程的名称如下：
《信息安全管理流程—安全补丁管理流程》配合《信息补丁管理办法》使用。
《信息安全管理流程—安全策略管理流程》：配合《信息安全策略管理办法》共同使用。
《信息安全管理流程—安全配置变更管理流程》：配合《信息安全配置变更管理办法》共同使用。
《信息安全管理流程—安全事件处理流程》：配合《信息安全应急响应管理办法》共同使用。
《信息安全管理流程—安全体系运作流程》：配合《信息安全安全体系管理办法》共同使用。
《信息安全管理流程—办公网络环境第三方人员访问申请审批流程》：配合《信息安全第三方人员安全管理办法》共同使用。
《信息安全管理流程—办公终端安全处理流程》：配合《信息安全办公终端管理办法》共同使用。
《信息安全管理流程—应急响应流程》：配合《信息安全应急响应管理办法》共同使用。
《信息安全管理流程—账号安全管理流程》：配合《信息安全账号口令及权限管理办法》共同使用。
安全技术规范
《信息安全网络技术安全规范—IP网络安全管理规范》：规范针对网络设备、网络结构以及网络各类安全控制的操作，确保安全配置和过程控制的安全有效。
《信息安全网络技术安全规范—防火墙配置标准》：规范系统的安全配置，降低被攻击的风险。
《信息安全主机技术安全规范—系统安全规范》：针对主流操作系统的配置安全，确保安全配置和过程控制的安全有效。
《信息安全主机技术安全规范—windows系统安全配置标准》：规范windows系统的安全配置，降低被攻击的风险。
《信息安全应用技术安全规范—应用系统安全规范》：规范应用系统在开发过程中，安全功能设定过程中的安全。
《信息安全数据安全规范—数据安全规范》：规范数据存储和传输过程中的安全控制。
《信息安全主机技术安全规范—应急技术安全规范》：规范应急计划的内容、应急流程、职责等相关应急准备。
保密协议
《第三方人员安全保密协议》；
《工作人员保密协议》。
安全管理机构
安全管理机构的规划，应以安全组织架构设计为基础，定义架构中涉及到的处室和岗位的职责以及管理方法，其内容包含但不少于等级保护基本要求中的第3级信息系统的管理要求中对管理机构的要求。
根据其在信息安全工作中扮演的不同角色进行优化组合的结果，反映了各处室在信息安全工作中的不同定位和相互协作关系。信息安全组织架构主要包括参与信息安全决策、管理、执行和监督工作的处室。
信息安全组织架构包含以下三个关键要素：
决定了信息安全工作中正式的报告关系，包括层级数和管理者的管理跨度；
决定了如何由个体组合成处室，再由处室到组织；
组织架构中包含了一套系统，以保证跨处室的有效沟通、合作与整合。
信息安全组织架构是开展信息安全工作的基础。在日常管理过程中，存在着多项信息安全管理事宜，需要对其中的重要事件进行决策，从而为信息安全管理提供导向与支持；对于所制定的信息安全管理方针需要进行有效的贯彻和落实；另外，对信息安全管理方针贯彻落实的情况还需要进行监督，以上各种情况都需要一个完善有效的信息安全组织架构来支撑。另外在未来信息安全保障体系建立的过程中，各种信息安全项目的开展将成为信息安全工作的一项重要内容，这也需要有相应的组织予以支持。
本方案提出的信息安全组织架构是以等级保护基本要求为指导，在借鉴国际最佳实践的基础上根据信息安全工作开展的需求进行完善的结果。
在完整的信息安全组织中一般包含以下几个重要组成部分：
信息安全决策机构
信息安全管理机构
信息安全执行机构
信息安全监管机构
以上组织机构的具体存在形式可以是多样的，如兼职的、虚拟的或者远程的。目前国际上普遍采用的信息安全组织架构如下图所示：

信息安全决策机构
信息安全决策机构处于整个信息安全组织架构的顶端，主要从高层领导的角度对于信息安全方面的工作进行指导和控制，信息安全决策机构应当是安全工作的最高决定者，主要职能包括：
确定信息安全工作的战略和方向
决定本项目信息安全组织
总体调配信息安全工作的资源
负责通过和决定信息安全策略和标准
对于信息安全方面的重大项目进行审批
在协调安全工作中协调各处室关系
一般信息安全决策机构在组织中的主要表现形式是由相关各处室主管负责人或代表组成的的信息安全领导委员会，参与人员主要取决于需要决策的内容。信息安全决策机构需要对信息安全工作开展中的重大事项进行决策，因此必须要有信息安全专职管理机构的代表；信息安全工作的需求来自于业务的开展，因此很多情况下也考虑各处室的代表的参与；信息安全决策工作中的一项重要课题是资源保障，因此往往需要分别拥有资金调配、人员调配和设备调配权力的处室的代表。至于对各处室选派代表的要求取决于决策机构的工作形式，一般来说需要有相关决定权力的人员作为代表。
信息安全管理机构
信息安全管理机构是整个信息安全管理体系建立和维护的组织者和管理者，它同时具有两种角色：
信息安全管理机构是信息安全决策机构的决策支持者，由管理机构为决策机构提供必要的决策所需信息；
信息安全管理机构是信息安全工作的规则制定者和决策推行的管理者。可以说是信息安全决策机构的执行组织，也可以说是信息安全执行机构的管理组织。
信息安全管理机构的职能主要包括：
整个系统信息安全相关政策标准的制定、更新
信息安全项目的规划、评审和质量控制
对信息安全工作的开展进行日常管理和监督
信息安全执行机构
信息安全执行机构主要负责具体信息安全工作的执行和开展。一般信息安全执行机构主要包括信息安全工程组织和信息安全运行组织两大类的组织。信息安全工程组织一般以独立项目小组的形式存在，由专门的信息安全开发和工程处室和相关工程人员组成。
信息安全工程组织主要负责的工作包括：
1.	信息安全基础建设
包括各项信息安全技术的实施，如认证授权与访问控制系统的建设，信息安全运营中心的建设等
2.	信息安全管理项目实施
信息安全管理项目的实施也是信息安全工程组织的重要工作之一，例如信息安全规划，信息资产识别与风险评估，信息安全标准与规范的制定等。
对于信息安全运行组织在成都中医药大学附属医院信息系统中主要负责日常信息系统监控维护方面的工作，并及时汇报日常运作中信息系统的安全情况。信息安全运行组织一般是一个虚拟的机构，包括运行维护、监控和技术支持在内的专职或兼职的信息安全人员，通常会分散安排在各个相关处室中，并统一向专门的信息安全运行管理人员汇报和负责。除此之外，专门的信息安全运营中心或是由外包商提供的监控服务也属于这一机构的范畴内。信息安全运行组织的主要职责可以概括如下：
依照各项管理政策、标准与规范、指南与细则开展工作
提供各种安全服务以直接支持业务，包括监控、事件响应、故障处理等
将工作中的各种需求和重要事项汇报给信息安全管理机构
接受管理机构和监督机构的监管、控制，并配合其开展工作
信息安全监管机构
信息安全监管机构的主要职能是对成都中医药大学附属医院信息系统内信息安全工作的开展情况进行独立的审查和监督。它可以是成都中医药大学附属医院信息系统的内部审计处室，也可以是独立的外部第三方审计机构，其主要职责如下：
1）监督各项信息安全策略、标准与规范、指南与细则的执行情况，检验信息安全管理机构和执行机构是否按照其开展工作。
2）检验信息安全管理机构和执行机构的工作效果，包括信息安全项目审查、信息安全服务效果审查，总体信息安全情况评估和信息系统安全性评价等工作。
信息安全监管机构是针对成都中医药大学附属医院信息系统信息安全管理机构和执行机构的工作进行监管，其审查监督的结果直接向信息安全决策机构或者成都中医药大学附属医院信息系统的决策层进行汇报，为信息安全组织改进工作提供支持。
信息安全角色和职责
从根本上来说，信息安全是系统中每一个和信息系统相关或是能影响信息系统的安全情况的人员的职责。每个人在信息系统的运行中，在不同岗位上都扮演着相应的角色。本部分将定义出系统中与信息安全工作相关的主要角色，并从总体上描述他们所承担的职责。
在信息安全工作方面一直在进行讨论的一个基本问题就是“到底是谁的职责？”，许多人对于信息安全相关职责仍停留在传统概念中，认为信息安全是信息技术处室或仅仅是信息安全处室的职责，这样给信息安全工作的开展带来了很大的困难。通过定义信息安全角色与职责，使成都中医药大学附属医院信息系统中每个工作人员都能找到自己的位置，同时为以后具体岗位职责的定义打下了坚实的基础。
通常在信息安全相关的角色主要包括几下几种：
高层管理人员
信息安全管理人员
处室和项目管理者/应用所有者
技术提供、维护和支持人员
管理支持者
用户
由于各自的角色不同他们在信息安全方面也承担着不同的职责。
信息安全组织架构和相关职责

信息安全领导小组
高层领导参加的信息安全领导小组，负责批准信息安全策略、分配安全责任并协调整个成都中医药大学附属医院信息系统范围的安全策略实施，确保对安全管理和建设有一个明确的方向并得到管理层的实际支持。信息安全领导小组应通过合理的责任分配和有效的资源管理促进成都中医药大学附属医院信息系统网络信息系统的安全。信息安全领导小组可以作为目前管理机构的一个组成部分。
信息安全领导小组有如下职责：
就整个人工智能与信息化部的信息安全的作用和责任达成一致；
审查和批准信息安全策略以及总体责任；
就信息安全的重要和原则性的方法、处理过程达成一致，并提供支持。如风险评估、机密信息分类方法等；
确保将安全作为制定业务建设和维护计划、内部信息系统建设的一个部分；
授权对安全控制措施是否完善进行评估，并协调新系统或新服务的特定信息安全控制措施的实施情况；
审查重大的信息安全事故，并协调改进措施；
审核信息安全建设和管理的重要活动，如重要安全项目建设、重要的安全管理措施出台等；
在整个组织中增加对信息安全工作支持的力度。
人工智能与信息化部
负责设计、建设安全管理体系，包括策略、组织和运作模式，并且进行宣贯和培训。
部门有如下职责：
贯彻执行政府相关主管处室有关网络及信息安全管理方面的方针、政策及各项工作要求，在各网上落实网络及信息安全的各项工作。通过等级保护工作保持与公安机关的联系，接受和执行公安机关的监督和指导。
负责建立信息安全策略体系，制定网络及信息安全工作制度及管理流程，起草、制定网络及信息安全的技术规范、标准及策略，聘请外部专家对网络及信息安全工作制度及管理流程进行评审，组织在全网范围内的实施。
组织、协调内部各处室实施网络及信息安全工作。
在成都中医药大学附属医院信息系统内开展信息安全知识共享，建立有针对信息安全的知识共享的技术平台，促进内部交流与学习。
定期组织内部人员或聘请外部单位，公安机关等进行全面安全检查，检查内容包括现有安全技术措施的有效性、安全配置与安全策略的一致性、安全管理制度的执行情况等；汇总安全检查数据，形成安全检查报告，并对安全检查结果在安全组织内召开会议进行通报。
安全维护组
负责项目日常安全维护工作，包括信息安全专员和各处室信息安全助理。
安全维护组有如下职责：
一、执行有关信息安全问题的处理
在日常维护中发现有安全问题，首先进行应急处理保证业务的连续性，然后通过提供安全事件报告的方式通知安全维护组相关人员，安全维护组人员在接到报告后，将和各专业组一起在保证业务正常运行的前提下解决安全问题，工作结束后，将由双方一起记录安全处理过程；
对重点主机系统的安全职责；
至少每月进行一次安全漏洞扫描；
对主机系统和网络设备上的用户进行审核，发现可疑的用户账号时及时向系统管理员核实并作相应的处理。
二、对网络设备的安全职责
监督信息安全管理机构制订的网络设备用户账号的管理制度的实行，在发现有可疑的用户账号时向网络管理员进行核实并采取相应的措施；
根据业务保护要求，提出防火墙系统的部署方案，并制订相应的信息安全访问控制策略。
三、对数据库的安全职责
协同数据库管理员对对数据库系统进行安全配置，修补已发现的漏洞；
协同数据库管理员对于数据库安全事件处理，并分析安全事件原因；
协同数据库管理员对于数据库安全事件进行处理，尽量减小安全事故和安全事件造成的损失，并从中吸取教训；
验证数据备份策略的有效性，对数据恢复过程进行试验，确保在发生安全问题时能够从数据备份中进行恢复；监督数据库管理员对重要数据的备份工作，对于重要数据的备份，必须每个月做一次检查，确保备份的内容和周期以及备份介质的保存符合有关的规定。
安全审计组
对用户的各种行为进行审计，对安全监控中心的各项监控、处理和维护工作进行审计。
安全审计组有如下职责：
依赖安全运行管理平台以及各种安全审计产品对管理网的用户行为进行审计。
对安全监控中心的各项监控、处理和维护工作进行审计。
安全监控中心
可利用现有的本项目安全信息管理平台，对网络进行全面的安全监控。
安全监控中心有如下职责：
查看安全运行管理平台的各种告警，做出处理判断，并编制下发工单
定期查看信息安全站点的安全公告，跟踪和研究各种信息安全漏洞和攻击手段，在发现可能影响信息安全的安全漏洞和攻击手段时，及时做出相应的对策，通知并指导系统管理员进行安全防范。
跟踪信息系统系统中使用的操作系统和通用应用系统最新版本和安全补丁程序的发布情况，在发现有新版本或者安全补丁出现发布时，通知并指导系统管理员进行升级或打补丁。
根据人工智能与信息化部提出的安全标准，对主机系统上开放的网络服务和端口进行检查，发现不需要开放的网络服务和端口时及时通知系统管理员进行关闭；
定期对主机的网络服务进行全面安全检测，在发现安全设置不当或存在安全漏洞时及时通知系统管理员进行修补；
根据安全管理机构规定的周期和时间，对网络设备进行全面信息安全扫描，发现安全网络设备上存在的异常开放的网络服务或者开放的网络服务存在安全漏洞时及时通知网络管理员采取相应的措施。
安全管理人员
人员录用
人员录用的应以《信息安全工作人员安全管理办法》为标准：对应聘者进行审查，确认其具有基本的专业技术水平，接受过安全意识教育和培训，能够掌握安全管理基本知识；对信息系统关键岗位的人员还应注重思想品质、历史方面的考察；
在签署劳动合同前，应由人力资源部进行人员背景、资质审查，技能考核等，合格者还要签署《工保密协议》方可上岗；安全管理人员应具有基本的系统安全风险分析和评估能力；
关键区域或部位的安全管理人员应选用实践证明精干、内行、忠实、可靠的人员，必要时可按JY人员条件配备。
人员离岗
人员离岗的应以《信息安全工作人员安全管理办法》为标准：立即中止被解雇的、退休的、辞职的或其他原因离开的人员的所有访问权限；收回所有相关证件、徽章、密钥、访问控制标记等；收回机构提供的设备等；
调离后的保密要求：管理层和信息系统关键岗位人员调离岗位，必须经系统人力资源部严格办理调离手续，承诺其调离后的保密要求；
离岗的审计要求：涉及组织机构管理层和信息系统关键岗位的人员调离单位，必须进行离岗安全审查，在审查合格后，方可调离；
对于在组织内进行岗位调动的工作人员，须根据新岗位的需要，增加、删除或修改该人员的计算机信息系统访问权限，包括电子邮件系统、业务应用系统、网络系统和其他计算机信息软硬件系统。与原岗位有关的所有资料文件，包括其软硬拷贝都需要移交，不允许私自带走。
安全意识教育和培训
定期的人员考核：应定期对各个岗位的人员进行不同侧重的安全认知和安全技能的考核，作为人员是否适合当前岗位的参考；
定期的人员审查：对关键岗位人员，应定期进行审查，如发现其违反安全规定，应控制使用；
管理有效性的审查：对关键岗位人员的工作，应通过例行考核进行审查，保证安全管理的有效性；并保留审查结果；
全面严格的审查：对所有安全岗位人员的工作，应通过全面考核进行审查，如发现其违反安全规定，应采取必要的应对措施。
应知应会要求：应让信息系统相关工作人员知晓信息的敏感性和信息安全的重要性，认识其自身的责任和安全违例会受到纪律惩罚，以及应掌握的信息安全基本知识和技能等；
有计划培训：制定并实施安全教育和培训计划，根据不同培训对象的需要，每季度或每半年进行安全培训，培养信息系统各类人员安全意识，并提供对安全政策和操作规程的认知教育和训练等；
针对不同岗位培训：针对不同岗位，制定不同的专业培训计划，包括安全知识、安全技术、安全标准、安全要求、法律责任和业务控制措施等；
按人员资质要求培训：对所有工作人员的安全资质进行定期检查和评估，使相应的安全教育成为组织机构工作计划的一部分；
培养安全意识自觉性：对所有工作人员进行相应的安全资质管理，并使安全意识成为所有工作人员的自觉存在。
外部人员访问与管理
应对硬件和软件维护人员，咨询人员，临时性的短期职位人员，以及辅助人员和外部服务人员等第三方人员签署包括不同安全责任的合同书或保密协议；规定各类人员的活动范围，进入计算机房需要得到批准，并有专人负责；第三方人员必须进行逻辑访问时，应划定范围并经过负责人批准，必要时应有人监督或陪同；
在重要区域，第三方人员必须进入或进行逻辑访问（包括近程访问和远程访问等）均应有书面申请、批准和过程记录，并有专人全程监督或陪同；进行逻辑访问应使用专门设置的临时用户，并进行审计；
关键区域管理要求：在关键区域，一般不允许第三方人员进入或进行逻辑访问；如确有必要，除有书面申请外，可采取由机构内部人员带为操作的方式，对结果进行必要的过滤后再提供第三方人员，并进行审计；必要时对上述过程进行风险评估和记录备案，并对相应风险采取必要的安全补救措施。
安全建设管理
系统定级和备案
由信息化管理处室负责业务系统的定级备案工作，由外部安全咨询服务团队提供技术支持，协助信息化管理开展系统定级备案工作；
明确信息系统的边界和安全保护等级；
以书面的形式说明确定信息系统为某个安全保护等级的方法和理由；
组织相关处室和有关安全技术专家对信息系统定级结果的合理性和正确性进行论证和审定；
确保信息系统的定级结果经过相关处室的批准；
制定《系统定级管理制度》，其基本内容包括：
明确负责系统定级的组织、岗位及职责，由信息安全领导小组总体负责，由人工智能与信息化部负责定级工作；
编制《信息系统定级基本情况表》模板，用于说明信息系统的边界和安全保护等级；
编制《信息系统定级报告》模板，用于说明某个系统定为某个安全保护等级的方法和理由；
编制《信息系统定级专家评审意见》。
制定《系统备案管理制度》：
指定责任处室：由安全管理部负责管理系统定级的相关材料并控制这些材料的使用；
将系统等级及相关材料报系统主管处室备案；
将系统等级及其他要求的备案材料报相应公安机关备案。
安全方案设计
由信息化管理处室负责，由外部安全咨询服务团队提供技术支持，开展安全方案设计工作：
根据系统的安全保护等级选择基本安全措施，并依据风险分析的结果补充和调整安全措施；
组织有关单位对信息系统的安全建设进行总体规划，制定近期和远期的安全建设工作计划；
根据信息系统的等级划分情况，统一考虑安全保障体系的总体安全策略、安全技术框架、安全管理策略、总体建设规划和详细设计方案，并形成配套文件；
组织相关处室和有关安全技术专家对总体安全策略、安全技术框架、安全管理策略、总体建设规划、详细设计方案等相关配套文件的合理性和正确性进行论证和审定，经过批准后，正式实施；
根据等级测评、安全评估的结果定期调整和修订总体安全策略、安全技术框架、安全管理策略、总体建设规划、详细设计方案等相关配套文件；
制定《安全方案设计管理制度》
指定负责处室。由人工智能与信息化部协同战略方案中心等有关处室负责安全方案设计；
规定方案设计流程、设计文档模板，包括：总体安全策略、安全技术框架、安全管理策略、总体建设规划、详细设计方案；
规定《专家评审意见》模板。
安全产品采购
制定《安全产品采购管理制度》，具体内容有：
1)	指定责任处室：由人工智能与信息化部会同他有关处室负责产品采购；
2)	定义采购流程：选型测试、年度审定及更新；
3)	确定采购和使用安全产品的国家有关规定；
4)	确定确国家密码主管处室对采购和使用密码产品的规定；
5)	定义选型测试所需文档的模板。
外包软件开发
制定《外包软件开发管理制度》.由人工智能与信息化部及有关软件开发管理处室、处室共同负责外包软件开发管理:
1)	根据开发需求检测软件质量；
2)	软件安装之前要检测软件包中可能存在的恶意代码；
3)	在合同中要求开发单位提供软件设计的相关文档和使用指南；
4)	在合同中要求开发单位提供软件源代码，并审查可能存在的后门；
5)	在合同中要求：在服务期内如发现安全漏洞，则开发单位必须及时提供相关安全补丁或者进行及时升级。
工程实施
制定《工程实施管理制度》:
1)	由人工智能与信息化部协同有关处室负责负责工程实施管理；
2)	督促施工单位或者处室制定详细的工程实施方案控制实施过程，并按照工程实施过程进行实施；
3)	维护并推行工程实施管理制度，明确说明实施过程的控制方法和人员行为准则。
测试验收
制定《测试验收管理制度》:
1)	指定人工智能与信息化部和有关处室共同负责测试验收管理；
2)	委托公正的第三方测试单位对系统进行安全性测试，并出具安全性测试报告；
3)	测评单位在测试验收前应根据设计方案或合同要求等制订测试验收方案，在测试验收过程中应详细记录测试验收结果，并形成测试验收报告；
4)	测评单位对系统测试验收的控制方法和人员行为准则进行书面规定；
5)	由人工智能与信息化部与相关处室对系统测试验收报告进行审定并签字确认。
系统交付
制定《系统交付管理制度》:
1)	由安全管理部负责系统交付的管理工作，按照管理规定的要求完成系统交付工作；
2)	制定详细的系统交付清单，根据交付清单对所交接的设备、软件和文档等进行清点。
3)	对负责系统运行维护的技术人员进行相应的技能培训；
4)	确保提供系统建设过程中的文档和指导用户进行系统运行维护的文档；
5)	定义系统交付控制方法和人员行为准则。
等级测评
制定《等级测评管理制度》：
1)	指定责任单位：由人工智能与信息化部负责，每年至少组织测评单位对系统进行一次等级测评，对发现的不符合项及时进行整改；
2)	在系统发生变更时及时申请对系统进行等级测评，发现级别发生变化的及时调整级别并进行安全改造，发现不符合相应等级保护标准要求的及时整改；
3)	选择具有国家相关技术资质和安全资质的测评单位进行等级测评。
安全服务商选择
制定《安全服务商管理制度》：
1)	指定责任处室：由安全管理部负责选择安全服务商；
2)	确保安全服务商的选择符合国家的有关规定；
3)	与选定的安全服务商签订与安全相关的协议，明确约定相关责任、服务范围、服务期限、服务各具体条款及服务质量要求等；
4)	确保选定的安全服务商提供技术培训和服务承诺，必要的与其签订服务合同。
安全运维管理
环境管理
制定《环境管理制度》：
1)	指定责任处室：由人工智能与信息化部同负责环境管理；
2)	定期对机房供配电、空调、温湿度控制等设施进行维护管理；
3)	负责机房安全，机房安全管理人员对机房的出入、服务器的开机或关机等工作进行管理；建立《机房安全管理制度》，对有关机房物理访问，物品带进、带出机房和机房环境安全等方面作出规定；
4)	规范办公环境人员行为，包括：工作人员调离办公室应立即交还该办公室钥匙、不在办公区接待来访人员、工作人员离开座位应确保终端计算机退出登录状态和桌面上没有包含敏感信息的文件等。
资产管理
制定《资产管理制度》：
1)	指定责任处室：由人工智能与信息化部及有关处室共同负责资产管理；
2)	编制并保存与信息系统相关的资产清单，包括资产责任处室、重要程度和所处位置等；
3)	规定信息系统资产管理的责任人员或责任处室，并规范资产管理和使用的行为；
4)	对信息分类与标识方法作出规定，根据资产的重要程度对资产进行标识管理，并对信息的使用、传输和存储等进行规范化管理；
5)	定义管理措施选择方案：根据资产的价值选择相应管理措施。
介质管理
建立《介质管理制度》：
1)	指定责任处室。由人工智能与信息化部负责介质管理；
2)	规定介质的存放环境、使用、维护和销毁；
3)	由人工智能与信息化部负责对存储环境进行专人管理，确保介质存放在安全的环境中，对各类介质进行控制和保护；
4)	对介质在物理传输过程中的人员选择、打包、交付等情况进行控制，对介质归档和查询等进行登记记录，并根据存档介质的目录清单定期盘点；
5)	对存储介质的使用过程、送出维修以及销毁等进行严格的管理，对带出工作环境的存储介质进行内容加密和监控管理，对送出维修或销毁的介质应首先清除介质中的敏感数据，对保密性较高的存储介质未经批准不得自行销毁；
6)	根据数据备份的需要对某些介质实行异地存储，存储地的环境要求和管理方法应与本地相同；
7)	对重要介质中的数据和软件采取加密存储，并根据所承载数据和软件的重要程度对介质进行分类和标识管理。
设备维护管理
制定《设备管理制度》：
1)	指定责任处室：由人工智能与信息化部及网络管理部等有关处室负责设备管理；
2)	对信息系统相关的各种设备（包括备份和冗余设备）、线路等每周进行维护管理；
3)	定义基于申报、审批和专人负责的设备安全管理方法，对信息系统的各种软硬件设备的选型、采购、发放、领用、维护、操作、维修等过程进行规范化管理；
4)	定义配套设施、软硬件维护方面的管理方法，明确维护人员的责任，对涉外维修和服务的审批、维修过程等监督控制方法进行说明；
5)	定义终端计算机、工作站、便携机、系统和网络等设备的操作和使用规范：针对主要设备（包括备份和冗余设备）的启动/停止、加电/断电等操作；
6)	定义信息处理设备带离机房或办公地点的审批流程。
漏洞和风险管理
1)	定期对系统进行漏洞扫描，识别安全漏洞和隐患，对发现的安全漏洞和隐患及时进行修补或评估可能的影响后进行修补； 
2)	定期开展安全测评，形成安全测评报告，采取措施应对发现的安全问题。
网络和系统安全管理
制定《网络和系统安全管理制度》：
1)	指定责任处室：由人工智能与信息化部负责网络安全管理，由信息管理处室专人负责对运行日志、网络监控记录的日常维护和报警信息分析和处理工作；
2)	对网络安全配置、日志保存时间、安全策略、升级与打补丁、口令更新周期等方面作出规定；
3)	定义更新流程：根据厂家提供的软件升级版本对网络设备进行更新，并在更新前对现有的重要文件进行备份；
4)	定义漏洞管理方法：定期对网络系统进行漏洞扫描，发现网络系统安全漏洞进行及时修补；
5)	定义设备配置方法：实现设备的最小服务配置，并对配置文件进行定期离线备份；
6)	定义外部连接审批流程：所有与外部系统的连接均得到授权和批准；
7)	定义设备接入策略：依据安全策略允许或者拒绝便携式和移动式设备的网络接入；
8)	定义非法上网管理方法：每周检查违反规定拨号上网或其他违反网络安全策略的行为。
9)	指定责任处室：由人工智能与信息化部负责系统安全管理，负责对系统进行管理，划分系统管理员角色，明确各个角色的权限、责任和风险，权限设定应当遵循最小授权原则；
10)	根据业务需求和系统安全分析确定系统的访问控制策略；
11)	每周进行漏洞扫描，对发现的系统安全漏洞及时进行修补；
12)	安装系统的最新补丁程序，在安装系统补丁前，首先在测试环境中测试通过，并对重要文件进行备份后，方可实施系统补丁程序的安装；
13)	依据操作手册对系统进行维护，详细记录操作日志，包括重要的日常操作、运行维护记录、参数的设置和修改等内容，严禁进行未经授权的操作；
14)	每周对运行日志和审计数据进行分析，以便及时发现异常行为。
对系统安全策略、安全配置、日志管理和日常操作流程等方面作出具体规定。
配置安全管理
1)	对记录和保存基本配置信息，包括网络拓扑结构、各个设备安装的软件组件、软件组件的版本和补丁信息、各个设备或软件组件的配置参数等信息进行安全管理； 
2)	将基本配置信息改变纳入变更范畴，实施对配置信息改变的控制，并及时更新基本配置信息库。 
恶意代码防范
制定《恶意代码防范管理制度》：
1)	指定责任处室：由安全管理部负责进行恶意代码防范；
2)	每年进行定期培训，通过培训提高所有用户的防病毒意识、及时告知防病毒软件版本、在读取移动存储设备上的数据以及网络上接收文件或邮件之前先进行病毒检查、对外来计算机或存储设备接入网络系统之前也应进行病毒检查；
3)	由专人工智能与信息化部负责对网络和主机进行恶意代码检测并保存检测记录，每周检查信息系统内各种产品的恶意代码库的升级情况并进行记录，对主机防病毒产品、防病毒网关和邮件防病毒网关上截获的危险病毒或恶意代码进行及时分析处理，并形成书面的报表和总结汇报；
4)	定义防恶意代码软件授权使用、恶意代码库升级、定期汇报等流程。
密码管理
建立《密码使用管理制度》：
1)	指定责任处室：人工智能与信息化部负责密码使用管理；
2)	总结在密码设备的采购、使用、维护、保修及报废的整个生命周期内的各项国家有关规定；
3)	严格执行上述规定。
变更管理
建立《变更管理制度》：
1)	指定责任处室：由人工智能与信息化部负责变更管理；
2)	建立变更流程：确认系统中要发生的变更，制定变更方案，系统发生变更前向主管领导申请，变更和变更方案经过评审、审批后方可实施变更，在实施后将变更情况向相关人员通告；
3)	建立《变更申报和审批程序》：对变更影响进行分析并文档化，记录变更实施过程，并妥善保存所有文档和记录；
4)	建立《中止变更程序》，中止变更并从失败变更中恢复，明确过程控制方法和人员职责，必要时对恢复过程进行演练。
备份及恢复管理
建立《备份及恢复管理制度》：
1)	指定责任处室：由人工智能与信息化部负责备份与恢复管理；
2)	识别需要定期备份的重要业务信息、系统数据及软件系统等；
3)	定义备份信息的备份方式、备份频度、存储介质和保存期等；
4)	根据数据的重要性和数据对系统运行的影响，制定数据的备份策略和恢复策略，备份策略须指明备份数据的放置场所、文件命名规则、介质替换频率和将数据离站运输的方法；
5)	建立《数据备份和恢复过程》，对备份过程进行记录，所有文件和记录应妥善保存；
6)	建立演练流程：每季度对恢复程序进行演练，检查和测试备份介质的有效性，确保可以在恢复程序规定的时间内完成备份的恢复。
安全事件处置
制定《安全事件处置管理制度》
1)	指定责任处室：由人工智能与信息化部负责安全事件处置；
2)	每年进行培训。通过培训让所有人能够报告所发现的安全弱点和可疑事件，但任何情况下用户均不应尝试验证弱点；
3)	制定《安全事件报告和处置管理程序》：明确安全事件的类型，规定安全事件的现场处理、事件报告和后期恢复的管理职责；确定事件的报告流程，响应和处置的范围、程度，以及处理方法等；在安全事件报告和响应处理过程中，分析和鉴定事件产生的原因，收集证据，记录处理过程，总结经验教训，制定防止再次发生的补救措施，过程形成的所有文件和记录均应妥善保存；为造成系统中断和造成信息泄密的安全事件制定不同的处理程序和报告程序；
4)	制定《安全事件等级划分方法》：根据国家相关管理处室对计算机安全事件等级划分方法和安全事件对本系统产生的影响，对本系统计算机安全事件进行等级划分。
应急预案管理
制定《应急预案管理制度》：
1)	指定责任处室：由人工智能与信息化部负责应急预案管理；
2)	建立统一的应急预案框架，框架应包括事件分级方法、各级事件启动应急预案的条件、应急处理流程、系统恢复流程、事后教育和培训等内容；
3)	在应急预案框架制定不同事件的应急预案，应急预案要指名适用的系统、设备等，要结合系统实际状况，如《门户网站被篡改应急预案》、《网络设备瘫痪应急预案》；
4)	资源承诺：从人力、设备、技术和财务等方面确保应急预案的执行有足够的资源保障；
5)	培训要求：对系统相关的人员进行应急预案培训，应急预案的培训应至少每年举办一次；
6)	演练要求：定期对应急预案进行演练，根据不同的应急恢复内容，确定演练的周期；
7)	更新要求：规定应急预案需要定期审查和根据实际情况更新的内容，并按照执行。
外包运维管理
1)	确保外包运维服务商的选择符合国家的有关规定； 
2)	与选定的外包运维服务商签订相关的协议，明确约定外包运维的范围、工作内容； 
3)	保证选择的外包运维服务商在技术和管理方面均应具有按照等级保护要求开展安全运维工作的能力，并将能力要求在签订的协议中明确； 
4)	在与外包运维服务商签订的协议中明确所有相关的安全要求，如可能涉及对敏感信息的访问、处理、存储要求，对IT基础设施中断服务的应急保障要求等。
大模型安全设计（大模型脱敏罩）
应用场景
通过精准身份管理、数据识别与脱敏、行为审计等功能，有效解决各行业数据安全痛点，保护敏感数据、规范数据访问、促进业务合规发展并维护行业数据安全秩序。
病历文件上传场景：医院在开展科研项目、远程医疗会诊等工作时，可能需将患者病历上传至大模型进行分析。系统对病历文件中的患者姓名、身份证号、医保卡号、详细病史等敏感信息进行精准脱敏，确保患者隐私安全。
临床诊断辅助场景：在辅助临床诊断时，医生输入患者症状描述等数据上传大模型时，系统即时对文本中的敏感信息脱敏，避免患者隐私泄露。例如，将具体家庭住址模糊化处理，仅保留所在区域信息，为大模型提供安全可用的数据，助力精准诊断。
医疗大数据分析场景：医疗机构利用大模型对海量医疗数据进行分析挖掘，以发现疾病流行趋势、优化治疗方案等。系统在数据上传至大模型前，对数据进行严格脱敏，确保原始患者数据的隐私，同时满足数据分析的要求。
功能设计
数据识别
MADA Mask数据智能识别引擎，通过敏感数据主动探测技术，基于丰富的敏感数据模型、多种识别规则和AI技术，能够准确识别和定位用户输入数据和上传文件中的敏感信息。

数据类型识别：将正则表达式、标准词、校验类、数据字典等传统识别方式和分布特征、重复特征、趋势特征OCR等多种AI识别算法相结合，准确识别敏感数据类型；
AI智能推理：通过人工智能与多源多维数据挖掘技术，利用行业知识库、上下文关联分析，学习数据中的特征和标签之间的关系，不断调整模型参数，以提高数据识别的准确性。
数据脱敏
业务流程

身份认证
用户通过浏览器、手机APP、PC客户端等方式访问大模型时，由MADA Mask进行身份校验，确认用户身份合法，保障数据安全和资源合法使用；
数据入栏
自动拦截用户输入数据和上传文件，通过数据智能围栏对其进行敏感数据识别和安全处置，避免用户将原始数据直接上传大模型；
安全处置
对用户输入数据、上传文件、模型输出数据进行敏感数据自动识别、数据实时脱敏、内容智能阻断等安全措施，避免敏感数据泄漏；
数据共享
完成安全处置后数据，共享给私域大模型或公域大模型进行推理使用；
结果返回
大模型返回数据经过数据智能围栏进行安全处理之后，将安全合规的数据返回给最终用户。
数据脱敏
MADA Mask系统采用数据替换、数据加密、数据泛化、数据扰动、数据删除等技术手段，能够根据数据类型和业务需求，灵活制定数据脱敏规则，同时通过数据增强技术在确保用户输入和上传文档中的敏感数据安全脱敏的同时，不影响大模型分析使用。

脱敏算法管理：结合数据的使用场景、敏感程度和合规要求，智能选择数据替换、数据加密、数据泛化、数据扰动、差分隐私、数据删除等脱敏算法，制定脱敏规则；
数据增强应用：由于数据脱敏之后会导致可用性下降，影响这模型训练或推理效果。因此采用数据脱敏增强技术，利用生成对抗网络GAN、变分自编码器VAE等技术生成与敏感数据相似的合成数据，再通过上下文补全技术，利用行业知识库和语言模型，恢复因为脱敏后导致的语义缺失，有效弥补数据脱敏过程中丢失的信息，提升大模型的训练效果。
输入数据脱敏
MADA Mask系统对用户输入数据进行严格的安全管控，通过数据智能围栏为用户提供输入数据管控通道：自动获取用户输入数据，并对其中的敏感信息实时脱敏，使得大模型获取的数据是脱敏后的安全数据，有效避免敏感信息泄漏风险。

上传文件脱敏
MADA Mask系统对用户上传文件进行严格的安全控制，通过数据智能围栏为用户提供上传文件的安全存储、处理和共享服务，实现安全且灵活的文件管理，满足不同业务场景下用户上传文件的安全使用需求。

私有区：为用户提供独立的数据准备区，支持用户导入、管理需要上传大模型的各类文件，并对上传文件进行敏感数据识别；
脱敏区：用户上传文件需要共享时，自动将私有区的文件摆渡到脱敏区进行实时脱敏，并将脱敏后文件摆渡到共享区；
共享区：只允许共享区的脱敏后文件，按需共享给不同的大模型或者用户使用，避免用户直接将原始文件提供给大模型导致数据泄漏。
内容智能阻断
系统运用先进NLP技术与算法，对每一次输入至大模型的内容展开实时且深度的扫描，一旦精准检测到高风险敏感信息，便毫秒级触发内容输入阻断功能。
检测精度高：精准识别各种复杂的敏感信息和违规内容。不仅可以识别已知的敏感信息模式，还能对新型、变体或经过伪装的敏感信息进行准确检测，减少误判和漏判的情况；
处理速度快：实现对大模型输入内容的瞬间扫描和阻断，满足对实时性要求高的应用需求。 

身份管理
通过对不同身份的细致管理，采取身份验证、权限设定等措施，确保系统的安全可靠，有力地保护了数据隐私。
多因素认证 (MFA)：支持多种认证方式（如密码、生物识别、硬件令牌等）的组合使用，同时引入中国移动基于号卡特性的实名认证能力。
行为审计
从文件上传的初始瞬间，到输入内容提交的关键节点，再到脱敏处理的核心环节，全流程中的每一个操作，无论是操作发生的精准时间，执行操作的具体人员，操作所涉及的详细内容，还是操作最终产生的结果都被记录。一旦出现安全问题，可通过审计日志快速追溯，查明原因，为安全改进提供依据。

会话分析：实时监控用户会话，通过实时意图分析引擎内置的20+种异常行为模型对内容进行识别和分析，针对不同的异常行为可精细化控制管控动作。

售后服务方案
售后服务期限
本项目免费维护期为三年，免费维护期以项目终验之日起算。免费维护期后，年维护费用另行商议，签订合同。
售后服务内容
对于本项目的售后服务，注重系统的维护服务方案，充分保障医院在维护期内享受到专业售后运行维护服务。本次售后服务面向本项目建设的所有信息系统及所涉及的操作系统、数据库、中间件，提供科学全面的售后服务，包括但不限于功能应用维护、完善性维护、适应性维护、纠错性维护、安全性维护、系统接口维护、系统数据维护、应急响应、系统培训、系统安装、系统调试、系统故障处理、系统迁移等，同时每季度应出具运行维护报告。
日常故障解决方案
现场运维服务
信息系统初步建成并投入运行后，出于受外界环境、医院内部组织结构变化及可能的人员操作不当的影响，不可避免地会产生各种各样的问题，为客户提供一个健壮的业务系统。
指导性响应服务
当系统发生问题而现场无我司技术人员时，首先由用户的技术人员对故障信息进行详细的观察记录，然后通过电话、短信、微信、QQ、邮件等通讯手段将问题通报给对接的项目负责人或专门的售后服务部门，由我司的技术专员与客户一起进行故障会诊，确认合适的解决方案，然后指导客户方人员进行现场操作，排除故障。
远程响应服务
当系统发生问题而通过指导性响应医院技术人员无法及时处理时，客户方技术人员利用有效手段向我司及时通报，由我司技术人员通过VPN或其他经用户授权的远程接入手段接入客户的网络系统，对网络、主机或应用故障进行远程在线诊断，确认合适的解决方案，指导客户方人员或直接远程操作进行故障处理。
非工作时间的立即响应服务
当在非正常工作时间内系统发生故障时，仍可以直接与我司售后服务部门联系，由我司售后服务部门协调售后服务人员提供立即响应服务，将在第一时间抵达故障现场，研究问题解决方案。
垃圾数据清理服务
在应用软件的运行中系统本身会产生部分垃圾数据，这部分数据对系统本身的运行具有负面作用，为了保证卫生系统稳定运行，这部分垃圾数据将定期清理，以充分保证系统运行的快捷和稳定。
功能持续完善和BUG修复服务
此次所投的产品经过多年的用户使用验证，质量可靠，值得信赖，但也不排除在系统升级和客户需求处理过程中存在BUG的可能性，当用户在使用系统过程中如发现存在BUG，应及时通报公司售后服务部门，我司将在最短时间内了解、分析BUG情况，并根据具体的情况制定出BUG处理方案，及时告知用户临时应急处理措施，避免业务受到更大的影响，对告知后续BUG处理所需要花费的时间及现场更新安排。对于各种BUG的处理，我司在24天内开发修补BUG的PATCH或者相应的处理办法。
性能调整优化服务
提出在正常条件下改进系统性能的各项建议，包括系统资源分配与效率提高建议、软件配置规划和性能优化建议、系统容量预测建议等。
场景融合创新
除了传统义诊和讲座，还可以探索更多创新场景，进一步丰富用户的服务体验：
中医药文化体验馆：在医院或合作社区开设中医药文化展示区，展示特色中医药文化，同时组织中医体验活动，让用户亲手体验制作药膳、学习艾灸推拿技法；
企事业单位合作：为企业员工提供定制化健康服务，如上门推拿、针对职场人群常见问题的中医健康讲座等；
校园健康计划：走进学校开展近视防控筛查、青春期健康教育等活动，培养青少年的健康意识。
项目实施计划
项目整体实施进度计划
本项目共分为两期建设，两期建设实施计划工期各为180个自然日。
第一期建设实施计划（180个自然日）
第一期建设将围绕统一门户平台的基础架构搭建、中医特色服务功能开发、线上线下一体化流程打通等核心内容展开，共分为两个阶段：
第一阶段：智慧门诊、智慧住院模块（90个自然日）
主要任务：建设智慧门诊，通过预约挂号、门诊支付、信息检索、排队等候、药品管理服务、自主开具处方以及报告解读等功能，实现了医疗服务的智能化。建设智慧住院服务，涵盖入院前的预约、办理手续、入院指南以及预缴费用；住院期间的每日费用清单和信息查询；以及出院后的总结报告、结算手续和病历复印等服务。
第二阶段：互联网医院（90个自然日）
主要任务：建设互联网医院：包含在线咨询服务、远程复诊、咨询转复诊、在线义诊、在线处方、处方流转、药事服务、在线购药、药品配送等功能模块。
第二期建设实施计划（180个自然日）
第一阶段：特色服务、中医专区、医保服务、便民服务（90个自然日）
主要任务：特色服务项目：慢病（专病）全程管理、中医延续性护理、日间手术服务等；中医专区提供：中药健康商城、中医名家团队咨询、中医健康教育等服务；实现与医疗保险系统的无缝对接，提供基于医保的在线支付、在线办理等服务功能；扩展便民服务项目，包括院内楼层分布信息查询、患者满意度调查、客户服务、就医指南等。
第二阶段：全流程功能核验优化（90个自然日）
主要任务：打造全流程AI智慧就医模块，包括AI客服、AI预问诊、AI分诊导诊等。
项目立项阶段
项目立项阶段主要管理工作包括：
关键环节
操作方案
计划完成时间
签订合同
招标完成后，签订商务合同。
开标结束后十个工作日内
确定项目组织架构
由双方领导组织讨论并确定项目组织架构；
双方共同确定各项目组组长和组成员；
项目经理向各组成员明确工作任务
合同签订后一周内
项目启动
召开项目启动会，宣布项目经理；
项目经理向客户方全体项目组成员宣贯项目相关实施任务、计划、实施要求；
明确甲乙双方各系统接口人；
甲乙双方建立一个目标一致、共同协作的项目团队；
项目经理介绍安全保密制度和要求。
制定项目进度计划
详细了解项目各项建设内容，与建设单位密切沟通，确定项目中重大里程碑；
按照重大里程碑节点，明确项目各阶段进度时间表，明确各阶段工作任务和人员安排；
任务落实到人头，任务目标要清晰，落实各阶段负责人；
各阶段进度时间表再细化，工作步骤和明细至少落实到周，对于关键任务要落实到天；
合同签订后十个工作日内
需求分析阶段
为确保成都中医药大学附属医院（四川省中医医院）互联网医院统一门户平台建设项目能够精准对接医院发展战略、满足临床业务与患者服务的实际需求，我们将围绕以下三个核心环节开展深入的需求分析工作。
建立中医特色的系统原型与需求确认机制
在项目前期，将基于成都中医药大学附属医院（四川省中医医院）的业务特点和服务模式，快速构建具有中医文化特色和现代医疗融合的服务原型。通过组织多轮次的现场调研与用户访谈，深入了解医院各科室（尤其是中医重点专科）、医护人员、患者群体的真实使用场景与功能诉求，全面覆盖治未病、智慧门诊、在线复诊、中药养生商城、护理上门服务、全病程管理等关键模块。
在此基础上，形成详尽的《需求规格说明书》，并采用可视化原型工具搭建交互式界面模型，辅助医院相关方直观理解功能实现路径。所有沟通记录、会议纪要、原型文档都将纳入配置管理流程，确保每项需求可追溯、可验证、无遗漏。
制定精细化的项目实施计划
随着需求逐步明确，项目经理将牵头组织技术、产品、测试等团队，对各项功能模块进行详细的工作分解与资源评估。依据医院提出的“第一期6个月+第二期6个月”的阶段性建设节奏，细化每一阶段的任务分配、开发周期、测试安排及上线节点，形成涵盖人员、时间、成本、风险控制在内的《项目实施计划书》。
计划中将重点考虑中医知识图谱构建、AI数字人集成、体质辨识算法训练、医患交互流程优化等关键技术难点，并预留合理的研发与测试缓冲周期，确保项目按期高质量交付。
制定符合中医业务逻辑的验收测试方案
测试团队将依据已确认的中医特色功能需求，制定覆盖全流程、全角色的系统验收测试方案。测试内容不仅包括常规的功能测试、性能测试、安全测试，还将特别针对中医问诊流程、AI预问诊推荐逻辑、体质辨识准确性、处方开单合规性、护理服务调度机制等业务场景进行专项验证。
同时，将与医院信息科、医务处、护理部等相关职能部门共同评审测试用例与验收标准，确保测试方案符合医院实际操作规范和监管要求。测试方案将作为正式受控文件纳入配置管理系统，为后续的系统上线与验收提供依据。
功能设计阶段
基于中医特色的功能设计与需求映射
项目产品管理团队、程序开发团队及中医业务专家共同参与功能设计全过程，严格依据《需求规格说明书》制定系统设计方案，重点覆盖中医在线咨询、智慧门诊、治未病服务、AI数字人、中药养生商城、护理上门服务、全病程管理等关键功能模块。
在系统概要设计阶段，项目经理负责组织需求与功能实现路径的逐项映射，建立《需求-设计追溯矩阵》，确保每一项中医特色需求都能在系统架构与功能逻辑中得到完整体现。同时，结合医院实际业务流程特点，优化用户体验设计，提升系统的易用性与中医文化感知度。
多维度的功能设计评审机制
完成初步功能设计后，由项目经理提交《功能设计说明书》至项目总监，由其组织技术、产品、测试、中医业务等相关职能组负责人召开功能设计评审会议。评审内容包括但不限于：
是否完整覆盖前期确认的中医特色功能需求；
设计是否符合医院现有业务流程与未来发展方向；
技术实现路径是否具备可行性与可扩展性；
用户界面交互逻辑是否贴合不同人群（如老年患者、基层医生等）的操作习惯。
通过多方评审意见汇总与反馈，形成最终确定版《功能设计说明书》，作为后续系统开发与测试工作的基础依据。
科学合理的测试计划发布
测试人员对系统功能设计文档的编制过程、格式规范、设计逻辑及可测性进行全面评价，确保其符合软件工程标准及相关行业规范。同时，对功能设计评审会议的组织流程、评审结果的有效性进行监督与评估。
对于不符合设计规范或存在潜在风险的设计内容，测试人员将以《不符合项报告》形式提交项目总监处理，并推动相关部门进行修正与优化，确保系统设计在技术可行性和业务适用性方面达到最优状态。
集成测试方案的定制化制定
基于中医业务系统之间的复杂交互关系，测试人员将依据功能设计内容，制定详尽的集成测试方案。
集成测试方案将明确测试目标、测试方法、预期结果及问题处理流程，确保各子系统之间高效协同、数据互通、业务流畅，为平台稳定运行提供有力保障。
投资估算与设备清单
投资估算明细
本项目建设软硬件及运营服务投资估算共计约1430万元。
软硬件建设内容包括AI智算平台、智能体开发与应用、数据洞察分析、互联网医院门户和互联网医院运营服务。其中AI智算平台包含本地算力中心和云端算力，投资估算约600万元；智能体开发平台（HiAgent）投资估算约80万元，10个中医智能体应用投资估算约200万元；数据洞察分析投资估算约150万元；互联网医院门户投资估算约250万元；互联网医院运营服务每年约150万元。
设备参考清单
AI智算平台配置清单
本地算力中心配置清单
序号需求项子项规格要求数量1GPU算力集群算力节点1、整机CPU总核数不低于96物理核，CPU主频不低于2.6 GHz；
2、整机内存不低于2048GB，DDR5
3、系统盘>=2块960GB SATA SSD本地盘；
4、数据盘>=4块3840GB NVMe SSD高性能本地盘；
5、每台服务器基于Ascend 910B ，显存：64GB 8；
6、GPU卡计算精度支持FP32、FP16、FP8、INT8、BF16，其中FP8精度下峰值性能不低于296TFLOPS、FP16算力不低于148TFLOPS
7、支持GPU卡间实现全互联，卡间互联带宽≥900 GB/s；
8、多机互联支持RDMA网络，RDMA网络带宽支持>=1.6Tb/s；
9、至少配置1块RAID卡，缓存>=4GB；
10、配置>=2块双口25Gb以太网卡。
11、8个热插拔电源模块，每个电源≥2700w42管理交换机1、48个10/100/1000M自适应电口和4个1G/10G SFP+光口，总计54个业务端口。 2、交换容量：2.4Tbps/24Tbps，包转发率660Mpps/762Mpps。 3、尺寸：1U高度 4、L2协议：支持STP/RSTP/MSTP、QinQ、链路聚合（LACP）、4K VLAN。 5、L3协议：IPv4/IPv6双栈、OSPF/BGP/RIP路由、VXLAN/EVPN。 6、组播：IGMP v1-v3、PIM-DM/SM/SSM、MLD Snooping 7、QoS策略：支持802.1p/DSCP优先级映射、SP/DRR队列调度、WRED拥塞避免13接入交换机1、配置48个SFP28端口，8个QSFP28端口； 2、交换容量≥ 4.8Tbps，包转发率≥2000Mpps 3、符合业界主流机柜的尺寸规范要求，设备高度≤1U（即高度≤44.5mm），设备深度≤400mm。 4、设备采用模块化电源，支持1+1冗余备份，支持热插拔；电源主备切换不影响业务正常传输；支持220V交流或240V高压直流供电。 5、为保证设备散热效果和可靠性，要求设备支持模块化风扇模块，其中风扇模块数量≥4个，支持风扇模块N+1冗余备份（N≥3）；所有风扇模块需要具有相同物理尺寸规格，以保证可任意框任意安装。 6、在海拔为0m~1800m范围内，设备长期工作温度0°~45°C。 7、支持跨设备链路聚合M-LAG，实现多台设备间的链路聚合。 8、支持N:1虚拟化：可将多台设备虚拟化成一台逻辑设备，虚拟组内设备具备统一的二层及三层转发表项，统一的管理界面、跨物理设备的链路聚合，并且链路故障的收敛时间最快为50ms。 9、支持VXLAN，包括：L2VNI Mapping，L3VNI Mapping。支持VxLAN网桥、VxLAN网关、EVPN VxLAN。24RDMA交换机1、固化400G QSFP-DD光接口≥64个，所有端口均支持线速转发；提供官网截图和链接证明。
2、设备高度≤4U，即≤177.8mm；设备深度≤760mm。
3、支持系统缓存≥110MB。
4、设备采用模块化电源模块，电源模块支持热插拔，支持N+M冗余备份，其中主电源模块N≥2个，备电源模块M≥2个；
5、为保证设备散热效果和可靠性，设备采用模块化风扇模块，支持风扇模块热插拔；模块化风扇模块≥8个，支持N+1冗余备份（其中N≥7）；所有风扇框需要具有相同物理尺寸规格，以保证可任意框任意安装；
6、支持PFC、ECN等数据中心特性；支持RDMA无损网络特性；
7、支持快速以太网链路检测协议，可快速检测链路的通断和光纤链路的单向性，并支持端口下的环路检测功能；
8、支持IPv4特性，支持静态路由、等价路由、策略路由、RIP、OSPFv2、BGP4、ISIS。
9、支持IPv6协议，包括静态路由、OSPFv3、BGP4+、IS-ISv6、等价路由、策略路由等。
10、支持sFlow网络监测技术，可提供完整的第二层到第四层信息，可以适应超大网络流量环境下的流量分析，让用户详细、实时地分析网络传输流的性能、趋势和存在的问题。
11、支持缓存状态监控，识别流量微突发情况；支持流量分析。
12、支持基于GRPC的Telemetry技术，实现对CPU、内存等信息的周期性采集。
13、支持标准ACL、扩展ACL、专家级ACL，支持二层到四层报文的匹配。
支持多种安全保护策略，包括CPU保护功能、网络基础保护功能、非法数据包检测、防DDos、防ARP攻击和防IP扫描、RADIUS/TACACS等，保护交换机在各种环境下稳定工作。
14、支持SNMP v1/v2c/v3协议；支持Telnet、Console、SSHv1/v2等配置方式；支持MGMT、RMON；支持FTP/TFTP文件上下载管理；支持热补丁功能，可在线进行补丁升级；支持零配置上线；支持NETCONF特性。
15、支持SP、WRR、WFQ、SP+WFQ、SP+WRR等多种队列调度机制；支持WRED、尾丢弃等拥塞避免机制。15耗材要求提供组网所需所有AOC线缆、光纤、光模块等耗材6云原生底座运营管理支持AI云原生底座，构建GPU算力统一资源池，满足大模型服务所需的k8s、数据库、中间件、存储等资源，可闭环满足大模型、大模型服务平台MaaS、的部署需求，并支持系统组件、硬件的监控告警等
需要支持用户登录时进行MFA多因素认证，从而对管理员类等权限大的用户登录时多加一道安全保护。
需要支持身份映射（第三方来源用户在平台的身份与权限映射管理）。
需要支持灵活的控制台显示设置：支持自定义登录页背景、控制台banner、logo等。7API网关
支持通过web ui创建api网关能力，并为API网关设置服务归属、版本、调用的角色（用户、运维、通用等）、是否支持对外访问、API的读写类型、后端超时时间等
支持对访问进行限流的能力，可设置QPS与QPM，支持整体限流策略、单账号限流策略、指定账号限流策略等，支持API熔断
支持对访问进行鉴权的能力，可支持Action鉴权、用户身份访问鉴权、跨域访问策略、访问白名单设置等，可禁止用户编程访问8运维管理需要支持可观测大屏，对整个系统的观测指标进行统计和汇总显示
告警需要支持邮件、站内信、IM（如飞书或微信）和告警回调的方式。
需要支持基于规则的告警聚合。可以基于标签定义聚合规则，实现告警的聚合，减少告警数量。
需要支持GPU监控，包含算力、显存、温度、功率等参数。
需要支持存储资源监控，包含数据卷使用量、剩余容量、io延时、读取带宽等。
需要支持自定义监控指标展示面板。9安装部署需要支持全流程自动化一键式部署，并展示进度。
需要支持客户组网模式选择，并提供组网拓扑信息查看。
需要支持软件部署过程中任务日志查看。
需要支持多个安装步骤并发执行。10机器学习平台队列管理支持将算力资源以队列形式进行划分，可以为队列分配CPU、内存、GPU等资源，队列支持开发机、在线服务、自定义任务等类别
支持对开发机、在线服务、自定义任务等负载类型任务的优先级设置进行调度，以保障重要业务的资源运行
需要支持配额管理，提升资源分配效率。11存储卷管理要求提供内置存储的能力，支持创建存储卷，并为存储卷分配存储容量。
要求支持通过web ui的方式将存储卷挂载给开发机、在线服务、自定义任务等不同的工作负载。12在线服务需要支持通过表单配置方式，一键部署多机并行的分布式推理服务
支持通过平台内置镜像（如 SGLang/MindIE/ mindspore） 等与自定义镜像、镜像URL等创建在线服务推理引擎
支持预置DeepSeek-R1-Distill-Qwen-32B、DeepSeek-V3-671B、DeepSeek-R1-671B等大模型与自定义模型
支持通过表单为在线服务创建实例，并可为实例设置副本数，分配CPU、内存、GPU等资源
需要支持推理服务的日志及状态监控（包含但不限于Token间延迟、首Token延迟、解码吞吐、端到端延迟、缓存命中率、排队中的请求等）
需要支持 OpenAI兼容 API 模型的在线调试。13开发机用于为开发者提供数据处理、在线编译、调试代码和模型开发能力，支持用户使用WebIDE方式进行在线开发，或通过 SSH 连接开发机远程开发。
需要提供常见场景的多架构开发机预置镜像。
支持保存开发机环境为新的自定义镜像。
需要支持一键登录 WebIDE
需要支持开发机的关机、重启等操作。
需要支持使用本地盘作为开发机根目录解决临时存储问题。
支持开发机查看 CPU、GPU、网卡和云盘的监控。
支持自定义网络端口。14自定义任务自定义任务，用于模型训练、精调、量化等任务场景。主要功能包括：
需要支持 PyTorchDDP 等多机多卡的分布式训练框架。
需要支持训练任务状态的 TensorBoard 监控，并设置存储卷以保持实验记录。
需要支持任务的状态监控、日志和 WebShell。
需要支持任务完成后，将新模型从存储卷转出至模型管理，以供后续调用。
支持为自定义任务设置最长运行时长，超过该时长任务将被系统自动取消；支持为自定义任务设置实例保留时长，实例完成或失败后环境可保留的时长，该时长不超过任务最长运行时间。15制品管理需要支持镜像仓库管理，通过内置镜像仓库服务，用户可以将自研或社区获得的容器镜像上传于此，以便后续推理、训练时使用；需要提供相对通用的内置镜像，如 cuda、NPU 等开发机镜像，SGLang、MindIE 等推理镜像，MindSpore 等训练镜像等。
需要支持模型和数据集管理，除了预置一些常用模型（如DeepSeek-R1和V3满血版以及各个蒸馏版）之外，还需开放模型和数据集的存储和版本管理能力；用户可以将从其他渠道获取到的模型或数据集上传到平台，以便在训练、推理和开发机中使用。
需要支持存储管理，提供基于对象存储加缓存系统的高速存储方案。用户可以按需创建存储卷，用于不同任务。
需要支持存储卷导出到模型仓库。16最佳实践要求平台内置最佳实践，可基于经验快速创建开发机，包括但不限于，使用预置模型与预置推理引擎多机一键部署DeepSeek满血版模型，使用自定义镜像部署嵌入和重排模型，开发机中微调大模型、蒸馏 DeepSeek-R1 训练的最佳实践等。17产品资质要求产品资质要求如下功能需求项需要演示证明：
支持模型的精调、蒸馏、强化学习等训练能力，并在产品界面上演示最佳实践，可基于经验快速创建开发机，包括但不限于，使用预置模型与预置推理引擎多机一键部署DeepSeek满血版模型，使用自定义镜像部署嵌入和重排模型，开发机中微调大模型、蒸馏 DeepSeek-R1 训练的最佳实践等。
在平台界面上演示支持API网关的能力，支持限流、鉴权的能力，并且提供可视化的界面配置操作。
在平台界面上演示可观测能力，支持在产品界面上对整个系统的观测指标进行统计和汇总显示。
在平台界面上演示支持算力配额管理的能力，需完整演示资源分配全流程，包括资源组创建、队列创建、工作负载类型的指定、CPU、内存和GPU资源的分配。
在平台界面上演示开发机功能，包括开发机创建、网络端口定义、一键登录 WebIDE，并使用WebIDE进行在线开发、开发机的关机、重启等操作。
在平台界面上演示内置镜像仓库服务，包括cuda、NPU 等开发机镜像，SGLang、MindIE 等推理镜像版本的创建，以及创建时所支持的构建方式，如手动上传等云端算力配置清单
序号需求项子项规格要求数量1GPU算力集群GPU云服务器CPU:180vCPU,主频不低于2.6GHz
内存：不低于1960G
GPU：不低于NVIDIA H20 *8
显存：不低于96G*8
本地存储：不低于4*3.84TB
RDMA：不低于400G*442云基础服务弹性块存储极速型SSD PL0单盘最大IOPS10000
单盘最大吞吐量180MB/s
单路随机写平均时延（Block Size=4K）0.3～0.5ms
承诺数据可靠性99.9999999%（9个9）40963NAT网关-小型最大连接数不低于10,000
每秒新建连接数不低于1,00014负载均衡CLB-中型I最大连接数不低于100000
新建连接数（CPS）不低于10000
每秒查询数（QPS）1000015对象存储-标准存储容量费用高可用、高可靠、高性能存储类型，适用于频繁被访问的数据
数据设计持久性（单 AZ）99.999999999%（11 个 9）
服务可用性（单 AZ）99.99%按量
6网络服务VPNVPN IPsec连接 500Mbps
通过加密连接的方式，在Internet网络中建立的临时、安全、可靠的通信隧道。通过VPN连接，可实现云上私有网络（Virtual Private Cloud，VPC）与本地数据中心、云上VPC与VPC之间的连接通信以及互联网客户端与VPC之间的安全访问按量智能体开发与应用清单
序号需求项子项规格要求数量1智能体开发平台创建智能体1、用户可在工作空间内创建AI应用智能体，智能体总数不低于5000个，应用类型包括对话型和流程编排型等，可设置智能体logo、功能介绍等参数
2、用户可以通过自然语言交互，快速生成一个智能体应用，并能在界面中跳转到智能体中进行编辑。1套2导入智能体用户可导入或者导出AI应用智能体，实现智能体的快速迁移
3智能体编排平台提供智能体编排功能，通过设置提示词、变量、知识库、插件、工作流、触发器、语音转文字、安全内容审查等参数，使用户能快速搭建智能体应用
1、支持定时触发任务功能，可按小时、天、周、月触发任务

2、支持事件触发任务功能，能根据Webhook URL发送请求，自动执行任务
4支持多种任务规划自动执行方式，以提升任务执行成功率。需要支持比如react、Function Call等方式
5应用编排支持提示词编写，提示词的编写是配置应用的重要一步，为应用设定身份和目标，提示词是给大语言模型的指令，指导其生成输出。用户可根据应用的实际表现优化和迭代提示词，从而达到模型体验的预期效果。6用户可根据自身业务需求、具体的应用场景以及所涉及数据的特质，选择平台已入驻的行业大模型，并可以配置不少于7种模型参数，以应对不同的智能体场景
7智能体发布用户可将搭建好的智能体进行发布并设置发布范围，供第三方使用或者集成，通过发布前审批功能实现智能体权限控制，并可记录智能体版本并还原。
1、支持多版本发布管理和配置还原，支持发布智能体中心，并支持私有和公有配置选项

2、支持对智能体的发布审批功能
8智能体调试在调试与预览区域，测试应用的实际表现，如果不符合预期，根据业务目标，分析不符合预期的原因，并继续调整和优化。9调用链tracing能力1、调试信息展示每个调用链和火焰图节点的相关数据，辅助开发者进行智能体性能跟token的优化
10智能体统计对于已发布的应用，用户可通过数据分析看板跟踪和分析应用运行情况，包括全部消息数、互动用户数、平均会话互动数、费用消耗（Token消耗）、Token输出速度和用户满意度等。11智能体概览平台提供对发布后应用的概览功能，包括应用名称、应用所有者、创建时间、web公开访问的URL和API访问凭据等。12智能体操作平台提供应用智能体统计、复制、限流、删除等功能。13对话记录1、平台可记录用户和智能体的全部对话，并记录全部敏感词日志，使模型更安全地输出
2、支持对接企业敏感词信息，并记录全部敏感词日志，使模型更安全地输出
14多agent模式平台支持多agent模式，通过多个智能体的流程调用，实现复杂业务的处理15插件中心用户可在平台上查看所有接入的插件，并对插件进行授权，实现能力分发16自定义插件创建1、支持用户基于已有服务自定义创建插件，支持Restful风格API规范，支持API key、Base Auth、Oauth2.0鉴权方式，并完成插件工具的基础配置、入参、出参、运行调试、授权方式的配置
17导入自定义插件平台提供导入外部插件功能，可通过Json、Yaml等文件方式方便用户快速导入创建插件。18自定义插件操作用户可对插件进行删除、导出、上架等相关操作，实现有效管理
插件发布前支持平台内置审核流程，以及对接第三方审核流程，对上架的插件资产做有效管理
19创建工作流在工作空间选择工作流管理创建工作流，创建完成后，可以在工作流管理的页面列表中看到该工作流。在列表中可进入工作流的编辑页面，初始状态下工作流包含start和end节点。20工作流配置在工作流编辑页面，可以通过拖拽的方式添加节点，配置节点的输入输出参数，并按照任务执行的顺序连接节点。
1、平台提供一个直观的用户界面，用户可以通过拖拽组件的方式，快速构建工作流程
21平台提供在工作流节点配置大语言模型参数处理文本生成任务。配置项包括模型类别、模型生成内容的随机度、输入参数、提示词和输出值等。22工作流支持在Code节点中使用IDE工具，编写自定义代码，来处理输入参数并返回结果。该节点支持JavaScript、Python运行时。23工作流支持在知识库节点可以根据参数从指定知识库内召回匹配的信息，配置项包括输入参数、知识库最少召回数量、最小相似度和输出值等。24工作流支持配置状态节点，该节点是一个if-else节点，用于设计工作流内的分支流程，连接多个下游分支，根据设定的条件按照顺序查找的方式来匹配运行的分支，如果匹配到某条件则只运行该条件对应的分支，否则继续匹配下一条件直至结束。25支持工作流运行状态查看，能够查看运行成功的工作流状态信息，在各节点的右上角单击展开运行结果可查看节点的输出。只有节点试运行成功，才可以发布工作流。26支持复制和删除等操作工作流的功能
允许用户将外部定义好的工作流导入到平台中，便于快速部署；用户可以将平台上的工作流导出，实现跨平台迁移或备份
27创建知识库在工作空间可通过知识库管理创建知识库，并可配置知识库名称、描述、模型、标签等信息。28知识文档管理知识库导入的文件支持结构化或非结构化数据，非结构化数据包含文本、网页、Markdown文档、Word 文档、PDF文档、图片等，结构化数据包含Excel表格、CSV 文件等。文件可以通过本地上传、在线下载、空文件三种方式上传。29为了提升召回的准确率，可以删除、新增或修改知识库的内容。在知识库页面，可以看到已创建的知识库和每个知识库内的文件名称、创建时间等信息。通过编辑功能可对知识库的名称、描述、索引等设置编辑。30平台支持自动分段和手工分段两种模式，来满足用户对于不同类型知识文档的多样化需求
1、支持对导入文件进行自定义切分，可以按版本自动切分，以及拥有丰富的内容处理规则，比如：移除空白字符，启用OCR，能自动关联文件名、标题、子标题等关键信息。
31平台可进行文档召回测试，通过设置文档召回权限、分段、相似度、检索模式等参数，可即时验证知识库效果32分段管理用户可在平台对知识分段进行预览、编辑、删除等操作，实现对知识文档的精确调优33知识增强与检索1、 知识库检索模式支持向量检索、全文检索和混合检索等3种及以上类型。

2、支持基于上下文内容进行RAG增强设置,允许将召回片段的上下文联合进行处理提高数据处理的准确率
34知识权限管理1、支持设置知识文件访问控制权限，可根据用户权限区分检索返回内容
35知识库操作平台支持知识库编辑、删除以及引用数查看等操作36问答库管理用户可在平台创建问答库，并对问答库进行管理，模型在回答时会优先检索问答库信息，实现对常见问题的精确回答37创建术语库用户可在平台创建术语库，并对术语库进行管理，模型在回答时会优先检索术语库信息，实现对专有知识的精确回答38创建数据库用户可在平台创建数据库，或者接入第三方数据库，实现表格知识的接入39数据表管理用户可上传表格文件，并对表格内容进行编辑和修改以及设置权限等操作，平台可对表格数据进行精确检索40数据库操作用户可对数据库进行编辑和删除操作41创建提示词模板提示词模板为智能体提供提示词的通用模板，并使用智能体直接引用。支持对提示词模板的编辑、复制、删除。42系统角色平台内置多种角色，并设置不同的角色权限43用户管理平台可创建、导入用户并对用户进行管理，用户数量不低于2000
44用户组管理平台支持自定义用户组，用户组可以添加人员和组织45组织管理平台可创建多级组织，并对组织成员、团队空间等进行增加、移除46模型列表平台可对模型进行接入和管理，用户可对模型按空间进行授权，实现模型权限控制47工作空间管理平台可设置工作空间，并对空间成员、权限、模型等参数进行管理，平台支持的工作空间不低于500个
支持不同空间使用不同模型
48智能体管理可查看平台所有智能体列表及用量统计
支持在后台展示智能体相关统计数据，包括：发布状态、版本数、会话数、对话术、Token消耗数、使用用户数、点赞/点踩数等指标
49标签管理可对平台的智能体和插件标签进行管理，包括创建、删除、启用、禁用等50插件管理平台内置100多不同种类的插件，满足用户不同场景的业务需求，可设置插件的上下架以及租户级别授权51干预规则列表用户可根据租户维度创建干预规则，对干预规则进行编辑，以及启用操作，对模型回答内容进行控制52用量分析租户维度用量分析，包括空间数量、开发者数量、智能体数量、对话数、会话数、资源数量（工作流、插件、知识等），可按租户进行切换53操作审计查看可查看、搜查平台的操作54操作审计下载可按需下载操作审计55租户管理平台支持创建多租户，租户和租户间实现数据、权限、模型的隔离，用户可设置每个租户的配额和有效期56用户管理可创建、编辑管理员用户，平台支持的用户量不低于10000个57插件管理可持续导入内置插件，并对内置插件进行分发58模型管理可配置模型接入，并设置模型权限59操作审计可查看、搜查运营端的用户操作60UI设置可设置logo、浏览器标题、icon等61SSO配置支持标准协议CAS/Oauth2/Ldap62多语言管理平台需支持至少3种语言的切换，以应对不同企业用户的需求63密码有效期策略支持设置密码过期时间64账号锁定策略支持设置登录失败的锁定策略65会话超时策略设置页面操作账号超时登出策略66安全要求整体安全要求无中高风险的安全漏洞，系统需要能够抵御一定程度的攻击。1套67智能体中医临床智能体围绕中医特色互联网医院应用，开发10个智能体应用。10个数据洞察分析清单
序号需求项子项规格要求数量1数据采集及处理能力内容数据采集社媒平台：抖音、快手、小红书、微博、公众号、知乎、B站、今日头条等1套2画像数据采集人口统计学特征：从年龄、性别、地域、手机品牌型号等基础信息维度分析用户群体分布。
消费偏好特征：消费品牌/品类偏好、消费频次、消费水平等
内容偏好特征：内容类型偏好、兴趣偏好、圈层偏好等3一方数据集成能力需支持企业一方数据库数据导入与上传，数据源类型包含但不限于mysql、oracle、starrocks、doris、Excel/CSV、Restful API、对象存储等4可视化数据建模提供拖拉拽等低代码方式对业务数据进行轻度ETL，并构建分析数据集的能力。5可视化图表展示通过多维度筛选控件和可视化图表形式，展现各项指标用户反馈情况；页面可配置对应维度的筛选控件6内容非结构化数据分析能力基于 AI 大模型技术进行文本分析和评论打标，根据处理后数据单独进行两级及以上的数据模型建立及训练7内容关键词抽取支持从用户反馈数据（发帖和评论） 中抽取重要的关键词8热门话题聚类无需预设标签体系 ，自动从用户发帖、点评或资讯数据中发掘典型意见、热门话题或者营销事件，从而发现一段时间内各用户群体的主要关注点或汽车领域话题的重要事件9篇章级情感分析针对整段点评或者发帖基于语义模型进行情感正负面分析，打 上 “正面 ” 、 “ 中性 ” 、 “负面 ” 的标签10AI agent能力多数据集管理支持一个智能体配置多个数据集，并支持对用户的问题进行语义识别来确定查询具体哪一个数据集1套11多智能体管理支持为不同的业务团队，比如人力资源，市场营销，研发，质量，供应链等部门配置专属的问数智能体，提供语义模型和业务知识配置功能12语义模型支持语义模型，提升问数效果。语义模型可以在数据集基础上对数据集名称、字段等信息做面向智能体使用者语义的重新设定，以提升智能体自动选择数据集和问答的准确性。13业务名词配置支持配置知识，包括行业黑话、业务逻辑、计算口径、代码示例等，降低问数门槛，提升问数效果。14自然语言问答对话式交互，基于自然语言描述，查询数据集、获取查询结果数据，并支持文件下载15支持多轮对话、记忆上下文16可视化分析，支持表格、趋势图、柱状图、指标卡等图表，可进行图表切换17高阶分析，支持维度归因分析18支持切换图表类型、查看SQL详情19支持用户收藏问题20支持历史问答记录查看21多终端支持支持钉钉（H5嵌入应用方式）22支持PC端智能体应用23支持问数API，支持以插件方式对接智能体平台24个性化配置支持为每个模糊问题配置多个拆解问题25支持配置推荐问题26支持配置自定义Prompt27AI大脑支持对用户的复杂分析需求进行深度思考，动态规划执行计划，充分调动模型能力和工具来执行分析任务，并最终形成包含数据查询、归因分析、执行建议的总结报告28业务经验库业务经验库可以让用户将本地文档，在线文档，会议纪要，IM对话导入到智能体内置的RAG和图谱中，构建领域知识体系，增强深度研究的专业性和准确性29复杂任务执行将自然语言描述转换为 Python 代码，用于执行更复杂的数据处理和分析任务，然后执行生成的 Python 代码，并获取执行结果。30知识检索在本地文档库中搜索相关文档，获取与用户问题相关的信息。31联网检索支持联网查询，可以搜索行业数据，研究报告，热点事件，天气预报等信息，结合企业内部知识和经验进行业务分析。互联网医院门户清单
患者端功能清单
模块
一级功能
二级功能
功能描述
专属门户
中医门户
中医特色门户
定制化特色设计，体现中医五行色系、突出中医名家和特色科室，体现中医养生理念。 将中医"辨证论治"思想转化为数字化交互逻辑，在保证专业性的同时，运用现代技术实现"千人千面"的中医健康服务。 门户设计会结合重点功能，核心功能及亮点突出，又能满足服务闭环。
适老化设计
就医适老化
1.就医流程简化和适老化：简化挂号、查报告、查订单等就医流程功能模块。 2.页面风格适老化：放大字体，UI突出。 3.老年版智能语音：结合大模型识别快速前往相关功能，功能导航。 4.老年版亲属待办：用户可以向亲属发送待办事件，亲属收到待办短信后，可以快速打开微信小程序，并看到希望他待办的事件。
首页待办中心
我的待办
支持根据用户的使用情况，动态在首页展示其待办事项，以便快速找到相关服务。包括： 1、进行中专病服务包； 2、待支付问诊订单； 3、待接诊问诊订单； 4、沟通中问诊订单； 5、待取药订单； 6、待检查事项； 7、待评价问诊订单； 8、待使用复诊权益； 9、待使用加号。 待办组件支持收缩与展开。
AI+智慧就医服务
AI+精准就医服务
AI客服
用户可向AI询问使用过程中的问题，并可转到人工客服。客服问题知识库可在后台维护。具体能力包括: 1.医学健康知识问答，医学健康知识问题的咨询解答 2.医院业务知识问答，支持医院自身业务知识内容的问答 3.服务分发，根据患者问询意图将医院服务进行服务的分发 4.语音识别，支持普通话/英语语音识别 5.数据统计与分析，支持数据统计，如患者常问的问题等
AI分诊导诊
通过AI医疗模型结合医院科室及医生画像，提供精准分诊导诊算法。具体服务及能力报告： 1.AI就医意图识别引擎，用于就医接待，基于AI模型对于患者就医意图进行分析，判断是否需要提供就医推荐服务，以及用户所需的就医服务的引导。 2.AI患者病情分析引擎，用于病情描述识别，通过AI医疗模型对患者病情进行问询，以及对患者症状、疾病的分析，完善患者画像，形成病情分析小结。 3.AI就医推荐引擎，用于就医方向指导，根据患者病情分析和报告解读结果，指导患者可能的就医方向。 4.根据引擎能力和算法，推荐就诊科室、推荐对症专家，根据AI病情分析引擎对患者症状/主诉的分析，结合各科室诊疗范围和科室内医生画像，智能判断患者可对症预约的诊疗科室和合适专家。 5.就医服务引导，根据患者病情分析及就医推荐的机构和医生所开通的服务，为患者提供挂号、咨询、复诊等服务的引导。
AI预问诊
患者面诊医生前，通过模拟临床医生问诊，帮助医生提前采集患者一诉五史病情信息，自动生成标准化预问诊小结，帮助医生提前了解患者病情信息并对接院内电子病历系统录入病例信息。具体能力项包括： 1.对话式交互功能：对话式交互询问患者病情信息，模拟真实对话流 2.预问诊小结：生成标准的结构化预问诊小结，支持医生增删改查，支持直接写入电子病历 3.AI病情分析：支持结构化展示病情，辅助医生提供诊断建议 4.科室模板配置：支持不同科室模板配置 5.症状描述辅助功能：症状描述支持通过图示、辅助文字来关联说明，以帮助患者更好理解症状
智慧门诊
门诊挂号
挂号界面
1.患者可以查看在掌上医院的挂号记录、候补记录，也可以查看其他渠道的挂号记录。 2.选择院区后可查看到相应的就诊日期、科室以及医生。该页面将自动呈现患者过往的挂号记录。 3.支持在挂号详情页面，进行中英文的切换，方便国际友人将英文界面切换成中文展示相关信息给院内相关工作人员。
预约挂号
1.提供全方位的院区导航服务，患者可以查看各个院区距离当前的距离、导航至院区的路线，助力用户全面选择适合自己的挂号院区。 2.支持预约线下多学科诊疗（MDT）号源，整合各科室专家资源，支持患者自己选择需要参与mdt的科室及专家级别（正高、副高），并可根据患者选择参与医生不同，实时计算挂号价格。确保患者能够及时获得由资深专业团队提供的全面综合评估及个性化治疗建议，从而提高诊疗效率和治疗效果，为患者提供更优质的医疗服务。 3.支持挂号候补功能，一旦有号源因故临时释放，系统将立即启动自动通知机制，为候补名单上的患者优先提供挂号机会，确保每一位患者都能公平、及时地获得就诊机会，极大缓解挂号难的问题。
当日挂号
1.支持当日号源的查询与筛选功能，用户可以通过系统查看当日可用的号源信息，并根据自身需求进行精确筛选，确保能够快速找到合适的就诊时间。 2.支持根据个人需求的便捷性，灵活选择最适合自己的科室、医生或名医团队。系统提供详细的医生介绍、专业特长，帮助用户了解每位医生的优势，从而做出更为明智的选择。 3.提供当日挂号在线支付的便捷服务，患者可以通过手机在线完成挂号费用的支付，无需亲自前往医院现场缴费，极大地节省了时间和精力，提升了就医体验的便捷性和舒适度。
预约取号
患者完成支付后凭借支付凭证进行取号，支持多种取号方式，避免了传统排队等待的烦琐和时间浪费。
预约退号
用户可以轻松查看和管理自己的挂号信息，并根据就诊需求和就诊计划选择退号操作。 1.仅针对尚未进行看诊的挂号，系统支持用户进行退号操作。 2.退号要求根据医院要求，对患者进行提醒和提示。 3.退号记录会计入预约规则及风控规则，减少频繁退号或恶意占号风险。 4.退号成功或失败均及时对患者进行提醒，并通过消息通道进行通知。
门诊缴费
门诊结算
系统支持对挂号、线上复诊、药品、检查检验等项目费用进行线上支付。 1、支持选择就诊人，（要求绑定病案号） 2、支持查看待缴费的项目 3、支持.在线自费支付缴费 4、支持查看缴费记录
医保结算
支持与HIS及医保系统对接后实现在线医保结算。
信息查询
信息查询
支持药品查询、费用查询、医院介绍、就医指南查询、发票查询、院内预留信息查询等。 具体如下： 1.查看药品目录及药品； 2.查看门诊支付费用信息及费用详情； 3.支持查看医院介绍； 4.支持查看院内导航图； 5.支持查看院内门诊、住院就医流程等就医指南； 6.支持查询电子发票； 7.支持修改用户在医院保存的预留手机号、地址等信息。
排队候诊
在线签到
支持患者在到达医院的范围内时，通过手机微信小程序签到并获取排队号码，减少患者在医院等待中的时间。
自助开单
检查检验服务
1.患者可以根据自己的需要自行选择和开具相应的检查项目。 2.支持后台配置，根据诊疗规范及管理要求允许患者自助选择的检查项目的范围。
检查检验报告查询
支持患者查看待预约的检查项目、在线预约检查时间，查询预约记录等；支持患者在线查看检查报告、检验报告、其他类型的报告（需系统对接支持）。
检查检验报告解读
支持患者在查询报告后链接至在线报告解读页面。
智慧住院
院前服务
入院预约
1.支持患者住院预约功能，便捷填写个人相关信息。 2.支持查询医生开具的住院证详情，包括姓名、年龄、性别等个人信息，以及住院病区的具体信息。 3.预约成功后，可实时查询预约状态。
入院办理
1.患者可以查看已开具的住院证信息，包括住院科室、床位安排、主治医生等详细信息，方便提前做好住院准备。 2.支持用户在线完成住院登记手续，避免了繁琐的线下排队流程。 3.支持在线缴纳住院预缴金的功能，可便捷地完成费用支付，无需前往医院窗口排队。
入院须知
1.根据医院提供的内容，为入院患者展示住院须知、入院流程、注意事项。 2.支持患者查看自己的主管医生、主管护师。
住院预缴
实现患者通过移动端在线充值住院押金，支持微信支付方式。同时支持查询历史充值明细信息、预缴金余额信息。
院中服务
住院一日清单
实现患者查询指定日期住院费用总额及费用分类明细清单，包含支付类型及明细，以便患者能够详细了解在特定日期内所产生的所有住院费用总额，以及这些费用是如何分类的。
住院清单查询
患者可以通过系统方便地查询自己在住院期间的各项费用清单，包括但不限于每日的诊疗费、药品费、检查检验费以及其他相关费用。
院后服务
出院结算
支持自费患者能够在线上轻松查看自己的出院结算信息。支持患者进行预交金的补缴。
出院小结
支持查阅出院小结文档，其中详细记录了患者的出入院时间、整个治疗过程的描述以及治疗所取得的具体结果等关键信息。
出院带药
支持用户方便地查看出院时所带药物的详细清单，帮助患者或其家属更好地了解出院后需要继续服用的药物信息，确保用药的准确性和连续性。
病案复印
支持提交病案复印申请。 1、支持选择就诊人（要求绑定病案号） 2、查询可复印的病案记录 3、选择复印份数、取病案方式（邮寄或自取） 4、支付复印费用 5、查询病案复印申请记录，查看排片、邮寄状态等
主管医护咨询
主管医护咨询
支持查看本次住院对应的主管医师，并可发起咨询服务。
互联网医院
在线诊疗
医护查询
支持根据名称、科室、疾病搜索医生，查看医生的基本信息、专业擅长、提供服务、平均响应时长、用户评价等信息。同时，还可以查看医生团队的详细信息，了解各医生的专长和团队构成，帮助用户找到合适的医生、团队。
医护智能排序
支持通过多种维度，对医护进行智能排序，包括以下维度： 1、医生服务量； 2、接诊率； 3、接单响应时长； 4、服务评价； 5、职称； 6、服务开通情况； 7、简介完善程度； 8、医生标签
在线复诊
支持用户发起在线复诊，选择期望开具的药品、检查检验项目，填写复诊内容，与医生进行复诊沟通
在线咨询
提供多样化的咨询选项，以满足不同用户的需求。功能包括： 团队咨询：用户可以直接向医疗团队发起咨询，团队成员可以团队名义共同协作处理患者的咨询。 图文咨询：通过文字和图片与医生进行在线咨询。 电话咨询：提交咨询后，可接到医生的虚拟通话，与医生电话沟通（支持到达医生约定的电话时长后自动挂断）。 视频咨询：提交咨询后，可接到医生的视频呼叫，与医生视频沟通。 极速咨询：用户可向科室发起咨询，科室的医生可以抢单回答，保证快速响应。 要求在沟通过程中支持： 1、网址识别； 2、二维码识别； 3、消息撤回、消息引用； 4、支持全屏输入模式
咨询转复诊
医生可以根据咨询情况将患者转为复诊，以便进行更深入的开单等延续性服务。
预就诊专区
支持患者通过预就诊专区，找到复诊医生，在线下就诊前进行线上预就诊，提前开具需要的检查检验单。
专科门诊
支持开展在线专科门诊，患者可以查看后台设置的各个专科值班医生，并向医生发起在线复诊。 支持科室筛选，上午门诊、下午门诊、夜间门诊的分开查看。
在线义诊
支持查看义诊活动介绍、参与医生，支持义诊服务、义诊价格、义诊名额等，并在符合条件时参与义诊活动。 支持提前查看义诊活动，并订阅义诊开始通知。
场景化咨询
支持提供专门场景的咨询专区，如用药咨询、护理咨询等。
药事服务
处方查看
我的处方：用户可以查看处方内容，包括药品信息、医生的签名等。
在线购药
系统支持患者在线缴费及取药功能。系统支持患者可通过线下药店取药、药品配送方式取药。支持： 1、患者点击购药，选择取药方式，填写收货信息 2、支付相关药品/物流费用 3、患者查看药品物流信息 4、患者也可以选择到院取药，在支付费用后查看取药相关信息
自助购药
患者自助选择需要的中药茶饮、院内制剂、OTC药品和养生保健用品，提交医生开方后可购买。
特色服务
全病程管理
病种管理中心
可对患者端展示的病种管理中心，支持查看医院正在管理中的病种以及病种对应的管护计划，患者可通过管护计划内容页申请入组。对于符合条件的申请患者，支持自动或人工审核通过。
专科服务中心
患者可查看各科室提供的专病的服务包，患者根据病情需要可选择对应科室及医生提供管护服务，打通在线支付。支付后，可自助使用服务内容，同时支持自动入驻到对应的付费专病管护服务内。 支持查看后台配置的专科服务中心banner、参与医生、团队等信息。
数字人管护师
签约到数字人管护师的，可通过数字人管护师对患者发送管护计划、服务内容、异常预警的处理方案。数字人管护师的形象IP可根据医院定制样式展示。
管护计划查看
纳管到专病计划后，可查看完整的管护计划，包括管护责任人、管护内的服务及监测项目，如设置可退组流程，支持用户取消签约入组。
专病计划内日程
可查看专病计划内自动触发的管护日程，包括宣教内容、随访通知、专病风险评估、问卷反馈、就诊提醒、用药提醒等管理路径内配置的服务项目。患者需根据配置的时间等日程有效期要求进行反馈，反馈结果会实时同步到管护责任人，并支持后台查看和数据统计。
异常监测与处理
对计划内配置的异常监测内容，包括数据指标异常、反馈信息异常等内容进行实时的监测，发现后自动上报。如果计划已配置异常处理方案，支持异常方案自动通知到患者。
专病个案管理
患者可根据入组的病种和管护计划配置的要求，查看、填写对应的专病管护档案，档案内根据后台配置字段展示患者基本信息、病史信息、检查检验等维度的数据。
数据指标监测
支持病种内需要日常监测的数据指标的采集，包括患者手动上传和接口自动同步两种形式。数据指标包括体温、体重等健康指征数据和血压、血糖等疾病风险指标。支持阶段性的数据统计与分析，呈现方式可支持图表形式。
在线沟通
支持医护人员在专病管理后台，对于重点关注患者、异常预警患者进行主动在线随访，患者可以在全病程管理中心查看到医护人员发送的消息；在医护人员设置的可回复条件内，可通过回话回复医护的消息。
健康指标
支持患者上传血压、血糖、BMI能核心健康指标，异常情况支持咨询数字管护师。
三师共管
患者加入管理计划后，自动创建患者专属健康管理医护群，实现三师或者五师共管患者，患者可以在群里进行咨询。
中医延续护理
护理区域门户
支持打造区域化中医护理服务平台，以省中医院为牵头机构，支持多机构入驻，协同运营、统一培训、规范管理的服务体系。 护理区域门户设计会结合重点功能，核心功能及亮点突出，又能满足服务闭环，包括护理咨询、就医陪同、护理上门、中医特色服务等服务类目。
服务查找
服务展示：支持按照服务分类展示服务项目，便于患者查找所需的服务项目，并可展示最近服务、推荐服务、专家和机构 服务搜索：提供全局和项目名称搜索功能，便于患者查找所需的服务项目或商品。 服务查看：服务内容显示及查看，服务显示服务项目介绍、服务项目原价、优惠价、已预约数量、预约须知、服务项目相关的用户评价等信息；
服务下单
支持预约护理人员上门服务，预约时需明确本次服务需求，例如服务时间、服务地址等，便于患者本人/患者代理人快速下单； 1.支持自动定位和手动填写地址，并可线上签署《知情同意书》。 2.系统支持在预约过程中未在指定时间内完成支付，自动取消服务预约。 3.支持调用百度、高德等地图组件，计算服务地点与医护执业地点之间的路程距离，以确认上门服务路程费用
订单管理
首页支持滚动展示用户下单动态；可查看所有护理服务的记录，包含服务状态、服务详情、服务费用等信息；对于已完成服务，支持对护理服务进行评价，包含文字评价和星级评价；支持再次预约，服务项目、服务医护等预约信息不需要再次填写，方便快速预约操作；
护理活动动态
开屏活动：用户开屏活动推送，点击后可跳转至具体活动页面或服务购买页面； 下单动态：首页滚动展示用户下单动态
关注医护
支持用户关注医护人员，关注后可从“我的关注”中快捷找到关注护士
账号及安全
身份管理：可通过获取用户授权信息创建账号，支持对就诊人进行实名认证，并可绑定多个就诊人 信息管理：支持用户个人信息管理，如绑定微信账号，服务地址管理，支持个人信息修改； 在上门护理服务中，支持通过核验二维码、手机号对服务对象身份认证，以确保服务的正确进行
支付管理
系统支持耗材费用、路程费用、服务费用的自动结算和线上支付。在上门护理服务中，支持服务过程中产生的追加订单、超时、服务费等进行支付，并支持退款
智慧服务向导
支持重点消息手机短信、微信服务通知双提醒；支持进入订单详情查看不同状态的提醒；
日间手术
日间手术全流程服务
系统支持生成日间手术中心二维码，引导用户扫码进入日间手术中心； 支持患者查看日间手术术前、术中、术后各个状态下，应该办理的流程引导，及使用相关服务，如看主管医生、术前事项、术中手术进度、术后服务等。
中医专区
AI中医体质辨识
AI体质辨识
用户跟着AI提示进行回答或上传必要信息（如舌诊照片等），AI分析并告知用户属于中医九种体质中的哪一种（比如气郁质，平和质等）。
智能体质报告
系统生成一份用户的专属体质报告，告诉用户测评后的中医体质、体质特点及要注意的健康教育指导包括饮食、运动调理建议等。
AI智能推荐
根据用户的体质辨识结果并结合用户的基本情况，及AI轻问诊的禁忌提问，AI智能推荐适合用户的茶饮处方，用户能看到推荐理由与使用建议，方便用户选择购买。
体质评测
用户根据系统引导完成体质辨识评测，AI智能推荐适合用户的茶饮处方，用户根据评测结果及智能推荐结果购买茶饮处方。
中医健康商城
中药茶饮
支持患者自助选择需要的中药茶饮，提供直接购买或咨询后购买两种方式。
查看健康单
支持患者查看开具的健康单，内容包括营养食品、养生茶饮及康复建议单等相关健康信息。
中医名医工作室
名医工作室
建立以名中医为核心的线上团队咨询服务模式，提供知名中医专家团队在线问诊服务，打造名中医线上服务口碑。
中医健康宣教
健康宣教
支持用户通过主动检索和分类查看的方式进行图文和视频健康知识宣教的查看。
护理宣教
支持宣教内容中推荐相关上门护理服务项目，点击后跳转至购买页面。
健康科普
查看病种配置的健康科普，查看日常推送的患教科普文章。
便民服务
院内导航
院内楼层分布查询
提供院内楼层、科室分布查询，允许用户通过查看医院内部楼层的静态图片、楼层分布信息，如院内楼层示意图等，从而轻松地找到他们所需要前往的目的地。
满意度调查
满意度调查
支持针对门诊、住院患者提供不同调查问卷。患者可以通过系统方便地查看并填写满意度调查问卷，从而为医院提供宝贵的反馈信息。
客服服务
电话客服
支持24小时线上客服答疑，同时支持工作时间拨打座机联系客服。
服务查询
服务条款查询、平台信息介绍等信息。
就医指南
就医指南
提供各种类型的就医指南和须知等重要信息，帮助就医患者更好地了解就医流程和注意事项。就医指南的分类，可根据配置分类，如按照门诊就医指南、住院就医指南，以便高效地利用医疗资源。
个人中心
账户管理
账户管理
系统支持注册、登录、登出和修改账户。
就诊人管理
就诊人管理
系统支持多种方式实名认证，绑定多个就诊人和关联院内病案号、就诊卡号功能，并可查看和编辑个人资料，查看平台业务使用记录（如挂号记录、咨询记录、处方记录、开单记录）等。
业务记录查询
业务记录查询
支持患者在个人中心查询挂号记录、诊疗记录、处方订单记录、个人权益、加号凭证等。
消息中心
消息中心
支持通过消息中心向用户发送各类消息提醒。
意见反馈
意见反馈
支持患者发起意见反馈，患者可以通过线上平台轻松提交他们的意见和建议。
医护端功能清单
模块
一级功能
二级功能
功能描述
医生工作台
在线诊疗服务
在线咨询
团队咨询：医生可以作为医疗团队的一员，响应并处理患者的咨询。
图文咨询：医生可以通过在线平台接收并回答患者通过文字和图片提交的咨询。
电话咨询：系统允许医生在预定时间内通过隐私电话（不泄漏医生个人手机号）与患者进行电话沟通，支持到达设置通话时长自动挂断功能，确保通话时间控制在约定范围内。
视频咨询：医生可以通过视频呼叫与患者沟通，更直观地了解患者状况。
极速咨询：支持医生对于面向科室发起的咨询进行抢单回答，这种机制确保了患者问题的及时处理。
咨询转复诊：医生有权根据咨询内容判断是否需要为患者升级为复诊，以进行进一步的开具检查、处方，从而提高治疗的连贯性和有效性。
AI病历书童
通过AI模型调用及算法优化，为医生根据咨询及问诊内容自动生成格式化病历，支持医生引用、修改。
在线复诊
提供与患者在线复诊的服务，支持医生写复诊意见、开处方、开检查检验单等。
复诊患者权益
向复诊患者提供免费咨询一次的权益二维码，方便医生为复诊患者提供延续性服务。
患者档案
查看患者的院内检查检验报告等档案。
智能消息设置
支持医生对于消息进行多维度设置，包括： 1、微信小程序消息通知权限的检测与开通； 2、消息免打扰时间段设置； 3、支持设置沟通中消息红点在已读后即消除或回复后才消除两种模式； 4、支持设置新订单通过微信或短信+微信提醒两种模式； 5、支持设置新订单在自定义时间未接诊提醒，最多支持设置5个时间点的提醒； 6、支持设置消息在自定义时间未回复的提醒，最多支持5个时间点的提醒，要求支持通过AI智能分析过滤无需医生回复的消息； 7、支持设置是否开通每日待处理日报，包括中午、晚上两个节点的选择。
开单服务
开检查检验
医生给患者开具检查单、检验单，线上选择开具项目、部位等信息，支持开单套餐的维护与开具。
开加号凭证
支持医生给患者开具线下就诊加号凭证。
记录查看
查看开单记录，开单项目、开单的患者等信息。
药事服务
开具处方
支持医生给患者开具处方单（支持西药、中成药），电子签名，支持处方的智能拆分。
处方审核
支持药师在线审核处方。
处方管理
支持查看处方记录，支持处方的召回。
医生工具
数据统计
支持医生查看服务量、评价等数据统计。
医护名片
提供医护人员对应的二维码名片等信息。
发布公告
支持医生发布公告，公告呈现在医生主页显示，公告支持有效期。
患者管理
患者管理中心
患者中心
支持对患者的档案、基本信息、重点指标、病史、住院记录、门诊记录、体检记录、检查报告、检验报告等信息查看，并可通过列表展示。
患者分组
对患者自主提交的入组申请进行审核操作；支持按分组维护查看患者列表；可对患者进行维护标签操作。
患者日程管理
可按事件类型、病种分组、日程状态等维度进行日程筛选，能查看并处理日程详情，包括填写问卷、查看宣教、查看通知提醒等。
医患在线沟通（1V1）
对于患者、医生之间进行线上沟通，沟通形式包含私聊、群聊，内容包含图文聊天、发量表、发宣教等等；可维护常用语。
医护群聊（1Vn）
患者入组后可以自动加入一个专属全病程健康管理群，患者在群里进行咨询，由医生、护士或个人管理师共同提供服务。支持发图文、量表和宣教内容。
患者异常事件处理
患者异常事件预警
医护人员可以在小程序接收到在管患者指标或者随访事项异常提醒，并支持通过在线沟通的方式对患者进行健康随访干预。
医患关系
医患关系设置
支持医生设置： 1、是否接受患者报到，报到相关提示内容，报到成功后欢迎语，报到成功后是否赠送沟通条数； 2、是否在患者咨询问诊后自动加入管理，可以设置仅对扫描复诊二维码的患者问诊后自动加入管理
我的粉丝
支持查看关注自己的粉丝新增数量、粉丝列表，包括粉丝关注时间、关注渠道，是否与自己发生过在线诊疗行为。可以主动将粉丝纳入管理。
报到患者审核
支持查看主动发起报到申请的患者、患者资料，支持进行审核拒绝，或审核通过后纳入管理
患者入组审核
医护人员可以在小程序端查看申请加入计划的申请并支持在线处理。包括同意申请和填写拒绝理由后拒绝申请。
纳管患者
支持将患者纳入管理，并分组标签，对纳管患者可以查看档案、主动发起沟通触达
群发管理
群发管理
支持医护人员在小程序端对患者进行批量发送提醒、随访、评估、宣教内容。支持查看群发内容的记录、发送成功的人次、是否已读情况。
重点关注
重点关注
按当前账号的数据权限，展示服务患者总人数等重点关注数据
数据分析
管理工具
支持患者管理、患者统计、管理计划等
消息提醒
消息列表
支持查看系统消息、日程消息、私聊、群聊消息等，并打通微信小程序服务消息，及时通过微信端进行消息提醒。
个人中心
账户管理
账户管理
系统支持注册、登录、登出和修改账户。
个人信息管理
个人信息管理
支持对个人信息的更新，如介绍、擅长等，以确保患者能够获取最新的专业背景信息。医生可以查看患者对其咨询服务的评价，这有助于医生自我评估和提高服务质量。提供医生名片，包括医生的专业信息，医生二维码，便于让患者快速找到医生。
平台服务介绍
平台服务介绍
提供客服联系方式、医院简介及相关条款的查看，以便患者了解平台信息。
运营特色服务
医生活动
年终一封信
支持医生查看年终配置的总结一封信内容，包括个人的年终数据、文案，支持医生生成自己的信件、分享信件、查看别人的信件等。
AI医生助理
AI医生运营助理
医生端设置AI医生运营助理，为医生提供基于AI模型和运营知识库为基础的运营策略、产品使用等服务支持，可了解最新的运营活动、产品操作手册、运营数据等。
护理上门（护士端）
服务过程管理
服务距离查看
支持护士查看订单距离，并对导航点进行第三方导航。
联系患者
支持护士通过虚拟号码联系患者。
服务管理
支持护士现场签到/签退，并监控服务时长；若超时护士可选择加收超时费；支持护士根据实际情况进行现场加单；支持医疗垃圾处理记录，支持拍照上传处理结果。
修改服务时间
支持护士与用户确认后，修改上门服务时间。
培训管理
培训管理
支持护士线上报名培训活动，查看培训信息，并查看已获取的证书。
护士安全
身份识别
支持护士签到时通过手机号、扫码方式进行患者身份核验。
一键报警
支持护士一键报警，将报警时经纬度发送给管理员。
业务设置
业务状态设置
支持对护理服务项目进行开启和关闭设置，仅开启中的服务能接收到患者的预约。
排班设置
支持自动排班和手动排班，仅排班时间能接受服务预约。
订单处理
订单处理
接取新订单时通知护士及时查看；支持护士查看患者订单基本病情信息，评估后可选择接单或转单、拒单；支持护士在抢单大厅进行抢单；支持护士对订单状态进行变更（如出发操作）。
账户管理
注册及登录
护士资质认证，具备相应工作经验以及职称的护士方可上岗服务，支持护士资质的在线提报及审核。
证书查看
护士通过某资质培训后产生的培训证书支持在线查看、导出。
名片管理
支持生成个人二维码，方便进行推广，扫码后直达名片主页。
信息管理
业务信息查看
支持护士查看自己的订单信息、用户评价等。
服务留痕
服务小结
支持护士根据各个服务项目配置的表单填写服务信息；支持填写医疗垃圾处理记录。
四级联动
远程会诊首页
查看远程会诊首页，含全部会诊记录。
会诊发起
支持填写、补充会诊申请单；支持不同机构医护间发起远程会诊；支持不同场景下发起远程会诊服务，如居家护理场景；支持选择指定会诊专家；按指定条件匹配会诊专家；提交即时、预约会诊申请；支持取消会诊。
支付费用
支持微信在线支付会诊费用；支持生成付费二维码，由他人手机扫码支付费用。
会诊评估
支持同意会诊、拒绝会诊操作，支持输入拒绝理由；支持调整会诊时间。
视频会诊
支持创建诊间，在诊间进行图文、语音沟通；支持选择成员发起视频会诊；支持视频窗口缩小放大；视频进行中支持邀请成员；支持会诊意见填写。
我的会诊
支持会诊评价；支持查看我的全部会诊。
会诊管理
支持会诊团队维护，包括团队类型、性质、团队人员、团队会诊价格；支持会诊医护维护，包括医护介绍，是否接收会诊，会诊价格维护。
会诊模板管理
会诊的模板维护，应用到科室、机构；发起人评估模板维护，应用到科室、机构。
会诊订单
全部会诊订单记录及订单详情查看。
综合管理后台功能清单
模块
一级功能
二级功能
功能描述
数据驾驶舱
数据驾驶舱
数据驾驶舱
围绕互联网医院业务运营核心指标，构建可视化数据中枢，实现动态监测与智能决策支持：具体功能及要求包括： 1.主要业务及当前成果展示； 2.各科室&医生服务排名情况； 3.用户流量及使用监测； 服务字段以业务系统数据为基础，驾驶舱设计满足医院管理需求。
业务数据分析平台
运营数据分析
1.支持用户及流量分析、业务数据分析、医护资源、经营情况数据分析。 2.用户分析：从用户角度出发，统计分析患者情况，如患者人数、患者年龄分布等。 3.医护资源分析：支持查看医生的行为数据分析，如医生接诊排名、平均评分、接诊速度等。 4.业务数据分析：支持按月、年统计不同业务类型的统计和分析数据，根据医院开展的业务来进行统计，如图文咨询、电话咨询、在线复诊、开单统计、预约挂号等。
业务数据分析
1、能够根据医生和科室的不同，对不同类型的业务进行详细的统计，如咨询、预约挂号、处方药品订单等。 2、支持对机构业务使用情况进行统计，如咨询、预约挂号、处方药品订单统计等。 3、支持按日维度分析，业务订单的趋势，如咨询申请量、咨询完成量、咨询收入趋势、预约挂号量、处方开方量等。
业务运营管理
预约挂号管理
预约挂号管理
支持挂号订单的查询与管理（包括挂号就诊人、就诊时间、预约/挂号科室等订单信息、支付信息记录）；支持挂号业务数据的基础统计；支持挂号业务的规则、须知的文字提示配置。
检查检验管理
检查检验管理
支持检查检验订单的查询与管理（包括开单医生、开单项目、检测人信息等）及预约订单管理；支持检查检验开单数据的基础统计（按科室、开单人统计）；支持检查检验业务的配置（检查/检验项目管理、开单套餐维护、自助开单人员病历配置等）。
在线缴费管理
在线缴费管理
查询患者在互联网医院上门诊缴费的记录。
在线诊疗管理
在线诊疗管理
支持专家咨询（图文、电话、视频）、极速咨询、团队咨询、在线复诊订单的查询与管理（包含患者信息、订单信息及沟通记录等），支持发起订单的退款；提供咨询订单履约明细的数据统计；支持极速咨询配置（服务规则、价格、科室/病种创建、接诊医生池管理）、团队诊疗配置（团队分类、成员创建、分成管理）以及便捷门诊排班管理。
院前服务管理
院前服务管理
支持院前中心配置（包括配置院前节点事件、服务等，生成用于患者扫码的院前中心二维码），并可查看院前中心使用记录（使用过院前中心的患者使用记录）。
日间手术管理
日间手术管理
支持日间手术中心配置（包括配置日间手术节点事件、服务等，生成用于患者扫码的日间手术中心二维码），并可查看日间手术使用记录。
药事服务管理平台
药事服务管理
支持机构开方数据的基本信息维护与参数配置，支持管理诊断、药品目录、商品目录、商品类目等信息的管理，支持维护患者端药品分类与信息（包括药品分类管理、药品信息与说明书维护）；支持查询药品订单记录（下单时间、收货信息等）；支持查看药事相关订单统计数据。
处方查询管理
支持查询处方信息（开单医生、开单内容、患者信息等）。
运营活动管理
运营活动管理
运营位管理
包括用户端、医护端banner等运营位置配置（包括banner图片上传，跳转小程序地址、h5地址、外部地址管理），设置首页大的专区图片（大的广告位，要求可以通屏展示，并可以配置搜索按钮、通屏图片、进入主按钮样式），及相关跳转的配置等。
活动搭建工具
支持自定义上传活动图片，并进行多个热区（自由选择点击区域）设置，支持不同热区跳转至不同功能。支持对各个热区的点击量进行数据统计。
义诊活动工具
义诊活动的配置，包括活动名称、介绍，活动有效期，活动参与医生、团队，参与价格、名额等。可以配置义诊活动排班表、接诊时间。
 年终一封信配置
支持配置年终一封信多页活动。支持自定义多页的背景图片、文案，支持40+变量的自由引入。支持开启页、内容页、分享页的配置。 支持对开启、分享活动的医生的数据统计
运营内容管理
评价管理
通过系统自动审核或人工审核方式进行审核评价，可以快速查找、筛选评价，可以根据差评找到相应服务订单进行诊疗过程追溯
意见反馈
用户反馈内容、反馈时间查看，对用户反馈进行跟进处理等记录
就医指南
对就医指南的内容进行配置和维护
用户及宣教管理
宣教管理
宣教板块管理
支持管理员维护宣教的一级分类。
宣教内容管理
支持管理员发布、修改、删除查找宣教内容，支持宣教内嵌入护理服务购买链接。
后台用户管理
后台用户管理
包括对管理后台的角色、权限及账户配置
患者用户管理
患者用户管理
支持患者端账号管理（注册时间、渠道、实名认证等信息），以及就诊人管理（就诊人注册时间、姓名、证件信息、住址、病案号等信息）。
公共支撑平台
终端配置管理
终端配置管理
支持配置小程序首页主模块、常用服务、门诊服务、住院服务模块的图标链接和主题样式（字体、按钮、图标颜色），维护平台公告及用户协议、隐私条款等内容。同时支持监管平台上报管理，包括推送订单管理、药品备案及业务监管备案设置。
机构管理
基础信息管理
支持对医院、科室、医生、护士等进行管理，并按照医院组织进行对应信息配置。
业务管理
医生业务配置管理
支持图文、电话、视频、极速咨询、团队咨询等业务的配置，包括开通的医生、团队，开通的价格、服务具体参数等。
支付管理
流水记录
查看平台收费的流水记录，可用于财务对账及医生结薪等场景。
退款管理
支持管理员查看退款情况、理由，批准退款操作。
专科专病患者管理
AI患者发现
AI患者发现
通过AI模型对接入的患者进行详细病情和病历进行分析，实现患者按疾病自动分组打标，筛选出专病待管理患者。
病种管理
病种管理
管理员可配置各科室需要进行管理的病种分类，用于创建专病管理计划、以及患者端的展示。
指标管理
支持指标的分类、指标设置和指标组的设置和编辑。其中指标项包括文本、数字、选项、日期/时间、图片等多种类型。同时可设置指标组模板，提高配置效率。
专病档案
可通过对接医院的信息系统内的指标和后台维护的病种指标，建立专病的档案，包括基本信息、病史信息、检查检验信息、现病史等维度，支持通过模板快速配置，支持患者端填写和后台维护两种方式。可用于患者建档、健康档案维护、专病档案维护等业务场景。
专病计划管理
智能管护路径
按病种创建管理计划和管理路径，通过打通院内患者数据，可按指标和就医流程实现精准患者自动入组、自动管护、异常自动上报、异常自动处理的全流程；支持病种管护计划签约到数字智能管护师，有专病数字人管护师进行管护任务的下发和信息回收。
管护服务事项
智能管护路径内支持配置宣教、文本提醒、表单等形式，并支持通过配置链接推送咨询、挂号、开单、预约检查等服务，可用于自动下发随访问卷、复诊提醒、用药提醒等场景。
异常事件配置
在管理路径内支持按专病病种管理要求，配置监测指标的异常阈值，并设置异常等级。超过阈值后，会进行异常自动上报。同时支持异常的预处理方案配置，可通过文字、表单、链接等方式让患者得到实时的异常问题解决方案。
管护计划维护
支持专病管护计划的维护，包括查看计划进度、计划内异常上报、计划内管理患者情况，支持关键数据指标的统计和分析。
患者中心
纳管患者管理
支持对患者的档案、基本信息、重点指标、病史、住院记录、门诊记录、体检记录、检查报告、检验报告等信息查看，并可通过列表展示。
患者日程管理
展现患者的量表、宣教、文本提醒等日程记录，含日程完成情况、完成时间；支持对日程进行手动增删补改；
患者入组审核
对患者自主提交的入组申请进行审核操作；支持按分组维护查看患者列表；可对患者进行移入、移出分组操作；
医护随访
医患在线沟通（1V1）
医护人员可以在管理后台对患者发起在线沟通，沟通通道包含私聊、群聊，内容包含图文聊天、发量表、发宣教等等；也支持向患者发送信息或拨打电话，可用于医护人员对重点患者或者发生异常患者进行主动随访和干预。
医患在线沟通（1Vn）
医护人员可以在后台对加入管理计划的患者进行团队管理。在群聊通道里团队成员均可以对患者进行管理。包含：回答患者咨询、为患者发送宣教、发送随访，评估量表等。
异常预警及处理
异常事件
查看异常事件详情，触发患者，触发原因等；根据患者名字、触发异常事件类型来筛选异常事件；处理异常事件时可关联查看患者档案，关联线上沟通功能
AI健管助手
辅助模式
医护人员在回复患者咨询问题时，如果想要减少打字或者回复内容更加系统性，可以打开辅助模式，由AI生成回复，医护一键复制回复患者。
托管模式
医护人员同时面临大量患者咨询或突然有事离开电脑，可以将回复的工作托管给AI来代替回答。AI回复的内容会经过医疗，医务的进一步合规审查，如果审查未通过，会自动拦截，回复内容审查通过后，会自动发送给患者。
专病随访知识库
健康宣教
支持按科室、类型、作者等维度进行健康宣教文章的创建和管理，并支持健康宣教的数据统计和分析。宣教内容包括图文、视频等形式，可用于疾病知识科普、生活方式指导、疾病康复指导等场景。
问卷表单
支持问卷的创建和维护，采用可视化的问卷编辑样式，问卷内选项包括单选、多选、文本填写、数字填写、逻辑跳转等，可用于满意度调查、诊后病情反馈、用药效果反馈等多种场景。
评估量表
支持问卷的创建和维护，评估表单选项包括单选、多选、文本填写、数字填写、逻辑跳转等，可计算表单得分，用于健康风险评估、疾病风险等级评估、 术后居家康复情况评估等多种场景。
平台配置
组织管理
创建医院、院区、科室、医护、团队信息，支持编辑、启用停用操作。
系统设置
账户创建、编辑、停用启用、密码重置、关联角色；角色维护及对应菜单权限和数据权限配置。
系统日志
对于在线沟通、计划创建及更改等操作记录留痕，确保系统操作可查看可追溯。
运营配置
客服配置
支持根据医院或者管理计划配置客服，支持拨打电话和长按微信二维码联系客服。
用户协议配置
支持按照医院或者管理计划单独配置患者入组的患者须知同意书。
护理上门业务管理
护理订单管理
护理订单管理
支持管理员后台查看全部订单情况，包括下单信息、支付信息、接单信息、签到签退记录、护理记录、患者自评、医疗垃圾处理记录等；支持后台发起退款（若院内提供退款接口，则可实现自动退款）；
护理服务管理
支持管理员对订单进行指派服务人员、重新指派服务人员
护理业务管理
机构项目信息管理
支持管理员维护机构信息，并配置护理服务项目基础信息、上架下架情况
模板配置管理
支持管理员后台配置患者评估模板、护理记录模板，并关联服务项目
护士排班管理
支持管理员后台对护士进行排班
护士评分管理
支持管理员查看护士评分
服务监控
服务监控
提供运行日志、服务监管，并支持实时接收护士报警响应；如紧急情况下护士发起报警，后台安全员将实时接收到地图定位以及订单详情的报警信息，以便即时干预；追溯护理人员的起点、终点位置，出门时间、签到时间、结束服务时间、护理记录、垃圾处理记录
任务调度
支持根据患者需求合理调度护士资源。
培训管理
培训管理
支持培训活动发布，维护培训活动上下架，审核培训报名人员，并在线授予证书。
平台管理
区域机构管理
支持区域内多机构、多角色账号的统筹管理，并支持根据角色分配业务权限。
后台账号管理
支持新增后台管理员账号信息，更改管理员密码；支持新增后台管理员账号。
业务信息管理
支持科室、医护人员、服务项目的基础信息维护。
后台账号权限管理
支持配置后台管理人员的访问权限。
黑名单管理
支持对用户进行黑名单管理，包括移入移出黑名单列表。
系统设置
弹窗管理
支持管理员发布、修改、新增开屏弹窗内容。
系统设置
包含服务超时费设置、路程费设置、抢单失效时长设置、收入分成设置等。
业务设置
包含banner设置、护士端公告设置等。
运营服务内容清单
序号
运营分类
运营内容
详细描述
1
品牌运营
互联网医院定制Logo设计
根据医院官方形象和色调，定制互联网医院专属logo，包括彩色、单色、反白等多种应用场景
2
互联网医院定制品牌物料设计
制作包括互联网医院平台宣传手册、海报、展架、台卡等在内的整套定制品牌物料
3
场景运营（不含线下场景装修）
互联网医院服务中心场景打造（门诊、住院）
设计互联网医院服务中心，包括空间布局、客户体验等，提供物料宣传布局
4
医院门诊与药品供应服务中心场景打造
优化门诊流程，设计便捷高效的药品供应服务场景，提升患者服务体验
5
用户运营
互联网医院平台客户服务
提供互联网医院服务相关的专业客户支持，包括在线咨询、电话咨询等。
6
全职在院医生助理
配备1名全职医生助理，协助医生开展线上线下服务
7
线上多平台引流
微信、支付宝、抖音、小红书等多平台精准流量运营，根据平台特性定制服务品牌推广策略
8
线下多场景引流（人工引流、客服引流、AI引流）
制作和分发院内外宣传材料，包括门诊物料、纸质单据线上化、院内住院大厅、护士台、病房、病区宣传栏等场景化宣传
9
医生运营
7*12小时医生专属服务机制
建立医生1v1运营服务和专属服务群，以便随时沟通协调医生的需求和问题，提供及时的解决方案和支持
10
建立医生互联网医院技能定期培训体系
定期调研医生执业需求，建立培训课件体系并开展科室培训，确保医生熟悉互联网医院规范，提升执业水平。
11
打造示范科室标杆医生线上服务案例
通过调研、整合患者案例和医院运营支持，建立日均50单以上的问诊量，打造3个示范科室和20名标杆医生的运营案例，并进行宣传推广
12
医生IP运营物料制作与投放
创建医生个人品牌的宣传资料，并通过诊室、社交媒体等渠道推广
13
定期医生沙龙活动
定期召开医生互联网医院执业经验分享活动，邀请优秀的互联网医院医生分享他们的成功案例、经验教训、技巧窍门等，激发医生的参与度和积极性，促进医生之间的交流和学习
14
AI机器人医生助手
7*24小时解决医生互联网医院操作常见问题、患者就诊引导就诊问题等
15
活动策划
各类专题活动策划及互联网义诊宣传
结合各类节日或疾病日主题活动，策划并执行互联网义诊等专题活动，扩大服务覆盖用户范围
16
专题宣教短视频与科普直播
制作专题宣教短视频、科普直播、图文内容，提升医生和服务品牌影响力
17
宣传及IP打造图文内容编辑制作
编辑制作高质量的图文内容，用于网络和线下宣传。
18
IP打造
互联网医院IP打造
全面打造互联网医院整体品牌形象，树立专业、便捷的服务标杆
19
特色科室IP打造
重点科室品牌形象塑造，突出专业特色和服务优势，建立科室品牌
20
名医团队IP打造
重点专家的互联网医院线上名医团队品牌打造与推广，突出线上名医团队的专业性和患者服务口碑，扩大名医团队影响力，引流精准患者
21
数据运营
可视化展示与报表分析
各项指标和数据展示以及各类日报、周报、月报数据分析。热门科室排行榜单、医生月度排行榜单、患者暖心评价等
22
数据预警与提醒
每日订单预警，日清日毕运营，监控未接诊订单、异常订单、失效订单等各类订单的数据预警并执行运营策略
23
运营策略决策支持
用户（患者、医护）流量监控与运营决策
24
治理体系搭建
准入管理
互联网医院机构及人员准入退出管理、创新业务准入审批管理等
25
业务管理
咨询业务、复诊业务、药事服务、互联网诊疗电子病历质控、健康服务等各类互联网+诊疗业务管理制度拟定、执行、监控
26
运营管理
互联网医院运营管理制度
网络安全服务清单（已补充）
序号需求项规格要求数量备注1内网外联防火墙标准2U设备，双电源；标准配置6个10/100M/1000M自适应千兆电接口、4个千兆SFP接口（不含SFP光模块）、4个万兆接口插槽（不含光模块），及1个接口扩展槽，标配64G SSD硬盘；默认支持下一代防火墙访问控制、入侵防御、网络防病毒、上网行为及URL分类管理、流控和IPSec VPN模块；整体最大吞吐量20Gbps，应用层最大吞吐量10Gbps，并发连接数230万，最大TCP并发连接数20万/秒，配置入侵防御、网络防病毒、上网行为及URL分类管理特征库3年升级服务，3年硬件维保服务2更新旧设备2外网互联网防火墙标准2U设备，双电源；标准配置8个10/100M/1000M自适应千兆电接口、4个千兆SFP接口（不含SFP光模块）、4个万兆接口插槽（不含光模块），及1个接口扩展槽，标配64G SSD硬盘；默认支持下一代防火墙访问控制、入侵防御、网络防病毒、上网行为及URL分类管理、流控和IPSec VPN模块；整体最大吞吐量60Gbps，应用层最大吞吐量16Gbps，并发连接数900万，最大TCP并发连接数30万/秒，配置入侵防御、网络防病毒、上网行为及URL分类管理特征库3年升级服务，3年硬件维保服务2更新旧设备3外网DMZ区防火墙标准2U设备，双电源；标准配置8个10/100M/1000M自适应千兆电接口、4个千兆SFP接口（不含SFP光模块）、4个万兆接口插槽（不含光模块），及1个接口扩展槽，标配64G SSD硬盘；默认支持下一代防火墙访问控制、入侵防御、网络防病毒、上网行为及URL分类管理、流控和IPSec VPN模块；整体最大吞吐量60Gbps，应用层最大吞吐量16Gbps，并发连接数900万，最大TCP并发连接数30万/秒，配置入侵防御、网络防病毒、上网行为及URL分类管理特征库3年升级服务，3年硬件维保服务1新增4WEB应用防火墙2U上架设备，1个HA口，1个RJ-45 Console口，1个10/100/1000 Base-T带外管理口，4个万兆光口，7个扩展插槽，2个USB口，双电源，含嵌入式软件，网络吞吐量75Gbps，最大HTTP吞吐量26Gbps，最大HTTPS吞吐量3.5Gbps，并发连接数1200万，配置3年特征库升级服务，3年硬件维保。1新增5网闸标准2U机箱，冗余电源，整机配备双液晶屏；2.内网接口：标配4个千兆电口，1个管理口，1个HA接口，6个千兆SFP插槽，2个万兆SFP+插槽，1个扩展槽位；3.外网主机接口：标配4个千兆电口，1个管理口，1个HA接口，6个千兆SFP插槽，2个万兆SFP+插槽，1个扩展槽位；全功能模块，包括文件交换、FTP访问、数据库传输、邮件传输、安全浏览、安全通道、消息模块，视频传模块和工控模块。网络吞吐量1.2Gbps，并发连接数20万，3年硬件维保。2替换并冗余配置6大模型脱敏罩系统实现在企业用户与大模型应用之间的数据交互过程中，对输入内容及上传文件进行识别与脱敏，输入管控与行为审计管理，有效防范数据泄露风险。主要功能包括：用户与大模型应用管理，脱敏管理，策略管理与审计管理。默认含200个用户数授权和5个常用大模型应用授权。含3年脱敏库升级服务，3年维保服务。1需额外配置服务器资源，推荐配置CPU：16核32线程•内存：32G•硬盘：4T编码开发阶段
中医特色系统的模块化编码开发
程序管理和开发团队依据《详细设计说明书》，围绕中医在线咨询、治未病服务、AI智慧就医、中药养生商城、护理上门服务、全病程管理系统等核心模块开展编码工作。采用面向对象的设计思想，构建高内聚、低耦合的系统架构，确保各模块具备良好的可扩展性与维护性。
在编码过程中，严格遵循国家医疗信息化标准及医院内部编码规范（如Java编码规范、接口命名规则、日志输出格式等），并结合中医业务逻辑的特殊性，对关键算法（如体质辨识模型、中医预问诊逻辑、药方推荐策略等）进行模块化封装，提升代码复用率与系统稳定性。
单元测试保障编码质量
为确保每一功能模块的功能完整性与逻辑准确性，开发人员与程序管理团队共同制定详细的单元测试方案。重点针对中医知识图谱调用、预问诊流程控制、处方开立合规性、护理服务调度算法等关键逻辑进行测试覆盖。
测试过程中使用自动化测试工具辅助执行测试用例，形成《BUG管理跟踪表》与《单元测试报告》，确保所有缺陷问题闭环处理，保障模块级代码质量。
多维度的代码评审机制
程序管理负责人定期组织代码评审会议，邀请项目经理、技术负责人、中医业务顾问参与评审。评审内容包括：
代码是否符合中医业务需求；
是否遵守编码规范与安全标准；
系统性能、可读性、可维护性是否达标；
关键算法是否稳定可靠，异常处理是否完善。
通过评审机制，确保编码成果既满足功能实现，又具备良好的可交付性和后期运维支持能力。
编码过程的质量控制与合规性评估
测试团队全程介入编码阶段，对开发过程中的编码方法、文档编制、评审流程等进行监督与评估，确保其符合软件开发质量管理规范（如CMMI、ISO 9001等）。对于不符合编码规范或存在潜在风险的代码段落，测试人员将以《不符合项报告》形式提交项目总监处理，并推动整改闭环。
此外，特别关注中医AI模块（如AI数字人、体质辨识、健康建议生成）的算法透明性与可解释性，确保其符合医疗行业对AI应用的监管要求。
集成测试阶段
中医业务全流程集成测试
由独立于开发团队的测试人员与医院相关业务部门共同执行验收测试，按照《集成测试方案》对以下重点业务流程进行验证：
中医在线问诊与预问诊系统的流程衔接；
智慧门诊与HIS系统的挂号、结算、检查申请等接口；
全病程管理系统中患者分组、任务下发、反馈回收等逻辑；
护理上门服务平台与GPS定位、身份核验、服务签到等功能的集成；
AI数字人与健康管理任务的自动触发与交互机制；
中药养生商城与中医体质辨识系统的联动推荐机制。
测试过程中形成完整的《BUG管理跟踪表》与《集成测试报告》，确保所有问题闭环修复，系统达到上线标准。
系统部署与文档移交
项目经理负责组织系统打包、安装与部署工作，形成包括《用户操作手册》《系统安装部署手册》《运维维护手册》在内的完整交付文档体系。系统从开发库迁移至生产配置管理库，完成最终产品版本封存。
同时，系统将完成与医院现有信息系统的对接测试，包括HIS、LIS、EMR、医保系统等，确保数据互通无误，业务流程无缝衔接。
项目总结与过程改进
项目完成后，项目经理牵头组织项目总结会议，全面回顾项目从需求分析、功能设计、编码实现到测试交付的全过程。重点总结以下内容：
各阶段工作成效与存在问题；
中医特色功能实现的经验与教训；
AI智能模块开发与测试的难点与对策；
跨系统集成过程中的挑战与解决方案；
对现有开发流程、测试规程提出改进建议。
形成的《项目总结报告》将作为后续项目管理与持续优化的重要参考，进一步提升医院在中医信息化项目建设方面的能力与水平。
风险识别阶段
根据经验，本项目实施过程中已知的及可预测的风险主要来源于以下方面：
技术风险
本项目涉及多种技术实现，可能会出现技术上的风险。由于团队成员大部分具备跨技术平台的技能和经验，而且熟悉相应技术，有着系统丰富的建设经验，技术风险出现的概率和影响相对较低。
同时响应多个紧急需求的风险
由于需要同时顾及多个业务部门的需求，如果出现紧急需求的并发高峰，将会对项目团队造成压力。
功能无法满足需求
这是常见的风险，在项目实施过程中，客户有时候没有完全清楚地描述需求内容，而开发人员也可能会误解需求内容，导致功能无法完全满足业务需要。
客户相关风险
根据我们的项目实施经验，沟通不及时是项目中客户相关的主要风险。即使双方分别选派各自的项目经理，指定统一的沟通方式、手段，但仍无法完全消除双方沟通的信息不对称、无法及时联系到接口人等情况，这也可能会对项目实施产生较严重的影响。
风险缓解、监控和管理机制
建立有系统的项目风险监控和管理机制，规定了项目风险识别、风险预测、风险评估的相应方法，并制定了不同风险的应对措施。可以缓解、监控和管理各种项目风险，主要的措施如下：
建立公司层面的项目风险管理委员会，负责对公司所有项目实施中的风险进行监控和管理；
定义各种可见或可预测的风险情况，并制定相应的监控因素、应对措施；
对立项的项目进行风险分析和评估，制定相应的风险应对计划；
定期检查各项目风险缓解、应对情况，调整风险应对计划；
总结每个项目的风险管理经验，积累项目风险应对的经验值等。
项目风险应对措施
作为风险管理的首要原则，我们应尽可能避免项目风险的出现。
技术风险：
由于项目团队成员大部分具备较强的开发技能和经验，而且熟悉相关技术，有着丰富的应用开发经验，技术风险出现的概率和产生的影响相对较低，并且我公司设有专门的研发团队，在出现紧急情况也可以给予有效的增援。
同时响应多个紧急需求的风险：除为本项目组建有针对性的项目团队之外，还建有研发团队、用户体验团队以及其他技术资源储备，可以随时根据项目工作的需要，增援项目团队，应对紧急、临时的任务需要。
为了避免客户和开发者之间沟通响应不及时的风险：可以在制定双方沟通计划时，针对各种可能出现的情况进行详细分析，并制定各种应急措施。
功能无法满足需求风险：根据项目的经验，我公司总结了一套行之有效的需求确认方法。通过快速、细致的原型系统开发，可以确保需求方和开发方直观、一致的认识，可以极大地降低功能无法满足业务需求的风险。从而避免重复开发，提高开发效率。
以上为针对已知及可预测风险的应对措施。为了有效地避免风险和减轻风险对项目的影响，我们还将制定风险监控计划，制定在项目进展时的监控因素。风险监控活动在项目开展的同时也同步进行，监控因素包括：项目组织的情况、项目需求变更的情况、实施进度、实施中出现的技术问题、其他因素
验收阶段
验收准备
当系统完成所有功能开发、集成测试、性能调优、安全加固等工作后，我公司将向成都中医药大学附属医院（四川省中医医院）提交正式书面验收申请，并附带以下资料：
系统部署文档与操作手册；
系统测试报告（含单元测试、集成测试、压力测试、安全测试）；
用户培训记录与满意度反馈报告；
中医特色功能验证报告（如AI预问诊、体质辨识、全病程管理等）；
数据迁移与接口对接清单；
医疗AI模型合规性证明材料。
验收组织
医院将组织由信息科、医务处、护理部、中医重点专科代表等组成的联合验收小组，对我公司提交的系统进行全面评审。评审内容包括：
系统功能是否满足合同约定与需求规格说明书要求；
是否支持中医特色业务流程与线上线下一体化服务；
系统运行稳定性、安全性、兼容性是否达标；
用户界面是否适老化、易用性强；
医疗AI模块是否具备可解释性与合规性；
是否完成与HIS、医保、药房、检验等系统的有效对接。
验收结论与交付
验收小组将出具《项目验收意见书》，明确系统是否通过验收。若验收通过，双方签署《项目终验报告》，并进入系统上线试运行阶段。若存在遗留问题，我公司将根据整改意见限期完成修复，再次提交复验。
风险及预期成效
本章将对项目在实施中存在的风险以及在落地使用所产生的效益进行分析。在本项目实施过程中，对项目中有可能存在的风险进行提前识别和分析，并建立相对应的风险对策和管理，能在项目实施过程中最大程度上减少因风险而带来的损失，使信息系统有效的建设、推进和优化，以保证项目的最终实施成功。同时评估项目在落实运行后所产生社会效益和经济效益，对项目的整体宏观规划具有重要的参考作用。
风险分析及对策
在项目实施过程中，对项目中有可能存在的风险进行提前识别和分析，并建立相对应的风险对策和管理，能在项目实施过程中最大程度上减少因风险而带来的损失，使信息系统有效的建设、推进和优化，以保证项目的最终实施成功。本系统在建设过程中可能遇到的风险以及对策管理措施如下：
风险识别分析
对本项目实施过程有可能产生影响并导致一定风险的因素主要有以下几个方面：
政策风险
本项目为创新业务场景较新，（1）互联网诊疗相关政策更新频繁，如《互联网诊疗管理办法》对在线复诊范围、电子处方流转等要求可能变化，导致现有功能合规性不足。（2）四川省医保对中医特色服务的线上报销政策尚未明确，可能制约中药配送、中医护理等业务。及数据安全与隐私保护法规对用户使用场景存在一定限制。 （3）数据安全法规细化，《数据安全法》《个人信息保护法》对医疗数据跨境传输、脱敏处理的要求升级，增加合规成本。为了尽量避免政策性变化带来的损失，须在设计的初期阶段将整个业务系统所面对的政策变化的可能性作全面的综合分析，并对一些可能发生的问题、模棱两可的问题讨论清楚，尽量将问题在初期阶段进行解决，减少政策性变化带来的影响。
管理风险
项目实施单位的项目管理能力是项目实施过程中系统质量的重要因素。本项目是AI创新应用建设项目，项目紧密连接医生临床工作、患者服务和医院管理等业务，需支撑应用服务建设、安全保障、标准制度制定、运维管理等多方面的工作，涉及多部门沟通，信息科、医务科、临床科室在系统运维、流程优化上职责交叉，影响“线上线下一体化”推进。，如果实施方没有建立起统筹管理和控制、分任务的管理和控制，对于项目实施的质量、实际应用效果都存在风险。
组织风险
本系统建设牵涉面广，涉及多个单位、项目牵涉部门众多，项目组织、控制和管理的力度直接关系到项目建设的成败。通过加强项目组织的力度、统筹考虑、统一领导、明确责任和分工，密切协作、统一技术实现方案、实施步骤和策略，才能有效地对整个系统的建设进行调控。
数据应用安全风险
本项目囊括全院各业务部门的医疗数据资源，为医疗业务应用提供重要支持，而且数据较为敏感，在数据应用过程中存在一定的安全风险，包括患者隐私泄露，AI大模型训练数据泄露、非法访问、越权访问、数据泄漏风险，数据丢失风险等，因此要确保在项目建设运营过程中的数据安全性。
云基础资源运行风险
本次项目建设中，需要实现云化服务，系统数据库和应用服务部署在x86系列服务器上，系统运行存在可靠性和性能等方面的风险。
风险对策
根据风险的不同类型采取不同的管理策略与工具，对风险进行控制，主要采取以下四种项目管理策略和技术，以消减或避免风险，完成应用软件和平台的设计和建设。
政策风险对策
1、主要涵盖已经明确政策的业务领域，部分政策细节未明确给项目带来的政策风险不大，在可控范围。在项目实施过程中密切跟踪相关政策前期研究及制定状况，在政策制定和业务管理模式设计过程中同步考虑信息化支持的兼容性。尽快完善相关的政策法规，研究制定标准化的业务办理模型，减少各地各部门之间存在的政策差异，确保信息化流程的通用性和适用性。
2、充分和业务部门沟通，在系统建设中随时跟踪业务部门业务需求的变更，及时的调整业务需求，做到系统建设也业务需求抱持一致。
3、逐步统一应用软件和管理模式，统一解决方案。预留医保接口柔性配置模块，支持报销目录、比例实时更新，优先对接省级医保在线结算系统，试点中医适宜技术线上报销场景。 3. 采用“本地推理+云端训练”架构，训练数据脱敏后上传，云端处理完毕即销毁，符合“数据不出省”监管要求。按等保三级标准建设，每季度开展合规审计。
管理风险对策
建议在项目建设过程中做好项目计划（应将项目沟通计划纳入项目计划中），及时沟通协调项目各方，加强监控和督促，保障项目实施按计划顺利进行。
为了规避项目实施中的管理风险，充分考虑本项目实际情况，如数据资源需求、资源限制、项目总工期限制等因素，参考同类项目实施的经验，制定切合实际的详细的项目基准计划，制定详细的项目实施方案。同时在项目实施过程中，根据基准计划采取有效措施对项目进行“三控两管一协调”，采用监理例会、专题会议、电话、邮件等方式及时沟通协调项目各方，加强项目的质量、进度、投资等要素进行监控。对于项目实施中出现的非预期情况进行分析，尽快采取恰当的措施进行处理，特别对于变更（项目范围变更、需求变更、方案变更、工期变更等等），应建立完善的变更管理和配置管理程序，确保项目始终处在受控状态。
在项目实施前期，要选择确定经验丰富的实施负责人，合理、明确制定项目的工作计划，落实项目管理的人员和职责，建立建设单位负责人、项目实施负责人、各项任务负责人的长效沟通机制，同时加强对实施方的过程监督管理，在项目实施过程中即时发现问题、解决问题，降低项目管理风险。
在项目管理过程中，将风险计划列入项目计划中，及时动态识别项目风险，定期评估已识别的风险清单和风险应对措施，并建立相应的管理储备对应对未识别出的风险。
建立双周联席机制，明确信息科负责系统运维、医务科管控诊疗规范、临床科室反馈业务需求，参照“整体规划，分步实施”原则推进。
组织风险对策
项目的复杂性和创新性决定了项目需要有强有力的项目组织保障，本项目尤其要重点加强相关政务部门的组织管理工作。为了规避或降低项目的组织风险，建议从领导层开始对本项目建设给予高度重视，让相关业务部门共同参与决策项目重大问题。
数据应用安全风险对策
在本项目设计的过程中要考虑到系统的安全性，建立完善的安全管理机制来保证数据在物理上和逻辑上的安全性。首先为基础数据平台设计数据备份、恢复机制，能够在发生不可预料的系统错误的情况下保证数据的安全性，建议采用双机备份、异地容灾备份等各种机制来保证数据的安全性。其次要保证数据的逻辑安全性，有完整的安全访问机制，保证数据不发生泄漏，即用户只能查看和自己业务相关和在自己授权范围之内的基础数据，对于其他的数据无权查看，从平台的访问到相关数据的访问都有详细的权限规定和限制。
云基础资源运行风险对策
风险规避计划：
1.评估X86服务器性能，对比X86服务器和小型机性能差异；
2.梳理医疗应用系统对小型机的依赖，做好应用解耦评估；
3.提前在X86服务器上部署应用服务，验证X86服务器对系统功能的影响，评估因功能或性能影响带来的周期；
4.系统完成后进行严格的性能测试，发现并整改系统的性能问题。
预期成效
完成本方案的建设，医院通过数字化转型实现运营模式革新，以数据驱动优化资源配置，构建 “线上线下一体化” 服务闭环，打造川派中医特色品牌矩阵，提升区域医疗竞争力与行业话语权。聚焦患者全生命周期健康需求，通过 AI 赋能重构就医流程，实现中医诊疗 “精准化、便捷化、个性化”，创新 “治未病” 服务模式，推动医疗服务从疾病治疗向健康管理转型，切实提升群众就医获得感与中医服务可及性。推进医院达到四川省智慧医院三星服务水平，以评促建，打造全国“AI+中医”智慧服务标杆。具体成效如下：
患者就医体验全面升级
通过 AI 预问诊、智能分诊导诊等功能，实现诊前流程优化，减少患者等待时间；在线复诊、药品配送等服务打破时空限制，尤其适老化设计满足老年群体需求；全病程管理覆盖 “防 - 筛 - 管 - 治” 全流程。
就医效率提升：线上预约挂号、分诊导诊使平均候诊时间从 60 分钟缩短至 20 分钟，年惠及患者 100 万人次，减少交通及时间成本约 6000万元。
可及性改善：远程诊疗覆盖 30 + 国家和地区，为海外侨胞、偏远地区患者提供年均 5000 例中医服务，体现中医药国际化公益价值。
适老化服务：老年版界面、亲属代办功能覆盖 50 岁以上患者，预计提升老年患者线上服务使用率至 40%。
分级诊疗推动：通过 “互联网 + 专病管理” 将糖尿病、不孕症等优势病种的复诊患者下沉至基层，年分流三甲医院门诊量 10%，缓解三甲医院压力。
医护服务效率显著提升
AI 病历书写、辅助诊断等工具自动生成标准化病历，减少文书工作负荷；智能排班、远程会诊系统优化资源调配，支持多学科协作诊疗；医护端全病程管理功能实现患者分组精细化管理/ 
医院运营管理提质增效
数据驾驶舱实时监控业务指标，助力科学决策；线上线下服务闭环整合门诊、住院、药事等流程，提升床位周转率与药品配送效率；安全架构通过数据加密、权限管控等保障医疗数据安全，符合三级等保要求；运营服务体系通过 IP 打造、线上流量运营，提升医院品牌影响力，预计年门诊量增长 20% 以上。
运营成本降低：线上复诊、云药房配送减少患者到院频次，预计降低门诊耗材消耗 15%、药品库存成本 10%，年节约运营成本约 150 万元。
中医特色服务创新发展
构建川派中医知识库与辨证论治平台，实现舌诊、脉诊等传统诊疗技术数字化；名医工作室与健康商城整合中医特色疗法与药食同源产品，推动 “治未病” 服务商业化；通过区块链技术实现道地药材全程溯源，提升中医诊疗可信度，助力中医文化传承与产业联动。
互联网服务拓展收益：实现互联网医院线上问诊100万年问诊量，依托平台提供中医健康商城、名医工作室等增值服务，预计三年之后每年达成5亿元收入。
科研教学能力跨越式提升
AI 智算平台支撑中医临床科研大数据分析，加速专病模型训练；远程教学与数字人培训系统打破地域限制，提升中医人才培养效率；临床数据与科研数据互通共享，推动中医诊疗标准化与成果转化，为建设国家中医临床研究基地提供技术支撑。
成都中医药大学附属医院（四川省中医医院）智能门户型互联网医院项目



成都中医药大学附属医院（四川省中医医院）智能门户型互联网医院项目

PAGE   \* MERGEFORMAT523


编制单位：三亚市人民医院
          
编制日期：2020年07月




