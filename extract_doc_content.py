#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from docx import Document

def extract_docx_content(file_path):
    """提取docx文件内容"""
    try:
        doc = Document(file_path)
        content = []
        
        print(f"正在提取文件: {file_path}")
        print(f"段落数量: {len(doc.paragraphs)}")
        
        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()
            if text:  # 只保留非空段落
                content.append(f"段落 {i+1}: {text}")
        
        return content
    except Exception as e:
        print(f"提取docx文件时出错: {e}")
        return []

def extract_doc_content_with_antiword(file_path):
    """使用antiword提取doc文件内容"""
    try:
        import subprocess
        result = subprocess.run(['antiword', file_path], 
                              capture_output=True, text=True, encoding='utf-8')
        if result.returncode == 0:
            return result.stdout.split('\n')
        else:
            print(f"antiword执行失败: {result.stderr}")
            return []
    except Exception as e:
        print(f"使用antiword提取doc文件时出错: {e}")
        return []

def main():
    # 处理原始项目文件
    doc_file = "成都中医药大学附属医院（四川省中医医院）智能门户型互联网医院项目0721 .doc"
    docx_file = "成都中医药大学附属医院智能门户型互联网医院项目PPT框架最终报告 (1).docx"
    
    print("=" * 80)
    print("提取原始项目文件内容")
    print("=" * 80)
    
    # 尝试提取doc文件内容
    if os.path.exists(doc_file):
        print(f"发现文件: {doc_file}")
        # 先尝试用antiword
        content = extract_doc_content_with_antiword(doc_file)
        if not content:
            print("antiword提取失败，尝试其他方法...")
            # 可以尝试其他方法
    
    print("\n" + "=" * 80)
    print("提取PPT框架文件内容")
    print("=" * 80)
    
    # 提取docx文件内容
    if os.path.exists(docx_file):
        print(f"发现文件: {docx_file}")
        framework_content = extract_docx_content(docx_file)
        
        # 保存框架内容到文件
        with open("ppt_framework.txt", "w", encoding="utf-8") as f:
            f.write("PPT框架内容:\n")
            f.write("=" * 50 + "\n")
            for line in framework_content:
                f.write(line + "\n")
        
        print(f"框架内容已保存到 ppt_framework.txt")
        print(f"提取了 {len(framework_content)} 个段落")
    
    # 尝试安装antiword来处理doc文件
    print("\n尝试安装antiword来处理.doc文件...")
    try:
        import subprocess
        result = subprocess.run(['which', 'antiword'], capture_output=True, text=True)
        if result.returncode != 0:
            print("antiword未安装，尝试安装...")
            # 在macOS上使用brew安装
            subprocess.run(['brew', 'install', 'antiword'], check=True)
            print("antiword安装成功")
        else:
            print("antiword已安装")
    except Exception as e:
        print(f"安装antiword失败: {e}")

if __name__ == "__main__":
    main()
